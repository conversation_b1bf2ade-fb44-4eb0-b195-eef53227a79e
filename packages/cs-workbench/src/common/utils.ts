/*
 * <AUTHOR>
 * @DateTime 2023-11-14 17:17:50
 */
declare global {
    interface Window {
        // 远程代码执行SDK
        remoteSDK: any;
    }
}

export function moneyStr(number: number, prepend?: string) {
    number = number || 0;
    let moneyStr = (Math.round(number * 100) / 100)
                    .toFixed(2)
                    .toString()
                    .replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ''));
    if (prepend) {
        if (moneyStr.startsWith('-')) {
            moneyStr = `-${prepend}${moneyStr.slice(1)}`;
        } else {
            moneyStr = `${prepend}${moneyStr}`;
        }
    }
    return moneyStr;
}

export function noop() {

}

export function isDef(val: any) {
    return typeof val !== 'undefined';
}

export function isFn(val: any) {
    return typeof val === 'function';
}

export function isArray(val: any) {
    return Array.isArray(val);
}

export function isString(val: any) {
    return typeof val === 'string';
}

export function isNumber(val: any) {
    return typeof val === 'number';
}

export function isPlainObject(val: any) {
    return Object.prototype.toString.call(val) === '[object Object]';
}

export function isObject(val: any) {
    return val !== null && typeof val === 'object';
}

export function isNone(val: any) {
    return val === null || val === undefined || val === '';
}

export function isEmptyValue(value: unknown) {
    if (Array.isArray(value)) {
        return !value.length;
    }
    if (isObject(value) && JSON.stringify(value) === '{}') {
        return true;
    }
    if (value === 0) {
        return false;
    }
    return !value;
}

export async function sleep(ms: number) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve(true);
        }, ms);
    });
}

export function clone(obj: any): any {
    if (obj == null || typeof obj != 'object') return obj;
    if (obj instanceof Array) {
        let copy = [];
        for (let i = 0, len = obj.length; i < len; ++i) {
            copy[i] = clone(obj[i]);
        }
        return copy;
    }
    if (obj instanceof Object) {
        let copy:Record<string, any> = {};
        for (let attr in obj) {
            if (obj.hasOwnProperty(attr)) copy[attr] = clone(obj[attr]);
        }
        return copy;
    }
}

export function keys<T>(o: T) {
    return Object.keys(o) as (keyof T)[];
}

export const dataURLToImage = (dataURL: string) => new Promise((resolve) => {
    const img: any = new Image();
    img.onload = () => resolve(img);
    img.src = dataURL;
});

export const canvasToFile = (canvas: HTMLCanvasElement, type: string, name: string, quality = 1): Promise<File | null> => new Promise(
    resolve => canvas.toBlob((blob: Blob | null) => {
        if (blob) {
            const file = new window.File([blob], name, { type });
            resolve(file);
        }
        resolve(null);
    }, type, quality),
);

export function flatDeepTree(source: any[], idKey = 'id', childKey = 'children') {
    const treeMap: any = new Map();
    let result: any[] = [];
    if (!source || !source.length) {
        return [];
    }
    source.forEach((item: any) => {
        treeMap.set(item[idKey], item);
        if (item[childKey] && item[childKey].length) {
            result = result.concat(flatDeepTree(item[childKey], idKey, childKey));
        }
    });
    result = result.concat(Array.from(treeMap.values()));
    return result;
}

export const dataURLtoFile = (dataUrl: string, filename?: string) => {
    let arr = dataUrl.split(',');
    let mime = arr?.[0]?.match(/:(.*?);/)?.[1];
    let bstr = atob(arr[1]), n = bstr.length;
    let u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename || '', { type: mime });
};

export const encodeQueryObject = (obj: any, excludeEmpty = false) => {
    if (!isPlainObject(obj)) {
        return '';
    }
    let str = '?';
    for (const key in obj) {
        if (obj[key] || obj[key] === 0 || excludeEmpty) {
            str += ((str.length === 1 ? '' : '&') + `${key}=${obj[key] ?? ''}`);
        }
    }
    return str;
};

/**
 * @description: 判断两个数组是否有相同的值
 * @date: 2024-06-13 14:49:13
 * @author: Horace
 * @param {any[]} array1
 * @param {any[]} array2
 * @return
*/
export function hasCommonValue(array1: any[], array2: any[]) {
    if (array1.length === 0 || array2.length === 0) {
        return false;
    }

    if (typeof array1[0] === 'object' && typeof array2[0] === 'object') {
        array1 = array1.map(item => JSON.stringify(item));
        array2 = array2.map(item => JSON.stringify(item));
    }

    return array1.some(item => array2.includes(item));
}
/**
 * @description: 解决IOS端时间格式问题
 * @date: 2024-12-27 16:14:18
 * @author: Horace
 * @param {any} date
 * @return
*/
export function newDate(date?: any): Date {
    // 判定时间为ISO格式
    if (date && typeof date === 'string' && date.match(/T/)) {
        const isoDate = new Date(date);
        if (!Number.isNaN(isoDate.getTime())) {
            return isoDate;
        }
        const isoDateArr = date.split(/[-T:+]/).map((item) => parseInt(item, 10));
        if (isoDateArr.length === 7) {
            return new Date(
                isoDateArr[0],
                isoDateArr[1] - 1,
                isoDateArr[2],
                isoDateArr[3],
                isoDateArr[4],
                isoDateArr[5],
            );
        }
    }
    if (date && typeof date === 'string') {
        // 将横杠替换为斜杠
        date = date.replace(/-/g, '/');
    }
    if (!date) {
        return new Date();
    }
    return new Date(date);
}
/**
 * @description: 格式化日期
 * @date: 2024-07-18 11:35:00
 * @author: Horace
 * @param {Date | string | number} date 时间
 * @param {string} fmt 格式
 * @return
*/
export function formatDate(date: Date | string | number, fmt = 'YYYY-MM-DD HH:mm:ss'): string {
    if (!date) {
        return '';
    }

    date = newDate(date);
    const obj: any = {
        'Y+': date.getFullYear(),
        'M+': date.getMonth() + 1,
        'D+': date.getDate(),
        'H+': date.getHours(),
        'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'S+': date.getMilliseconds(),
    };

    const week = ['天', '一', '二', '三', '四', '五', '六'];

    if (/(Y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }

    if (/(w+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length > 1 ? '星期' : '周') + week[date.getDay()]);
    }

    for (const k in obj) {
        if (new RegExp(`(${k})`).test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? obj[k] + '' : ('00' + obj[k]).substr(('' + obj[k]).length),
            );
        }
    }

    return fmt;
}