<script setup lang='ts'>
import { onMounted, onUnmounted } from 'vue';
import { useQiyu } from '@/views/tccc/hooks/useQiyu.ts';
import DaemonService from '@/service/daemon-service.ts';
import { useFollowUpService } from '@/views/follow-up/hook/follow-up-service.ts';
import { useRemotelyHook } from '@/views/remotely/page-management/hook/remotely.ts';
import { OaFloatNav } from '@abc-oa/components';

const { callout } = useQiyu();

const { handleRemotelyClick } = useRemotelyHook();

const {
    addFollowUpRecord,
    navigateToFollowUpRecordDetail,
} = useFollowUpService();

async function handleExternalMessage(msg) {
    switch (msg.type) {
        case 'callout':
        {
            const record = msg.payload;
            await addFollowUpRecord(record);
            navigateToFollowUpRecordDetail(record);
            await callout(record.phone, record.chainId, record.clinicId);
            break;
        }
        case 'followUp':
        {
            const record = msg.payload;
            await addFollowUpRecord(record);
            navigateToFollowUpRecordDetail(record);
            break;
        }
        case 'remotely':
        {
            const record = msg.payload;
            await addFollowUpRecord(record);
            navigateToFollowUpRecordDetail(record);
            await handleRemotelyClick(record);
            break;
        }
        default:
            break;
    }
}

onMounted(() => {
    DaemonService.getInstance().addExternalMessageHandler(handleExternalMessage);
    DaemonService.getInstance().start();
});

onUnmounted(() => {
    DaemonService.getInstance().removeExternalMessageHandler(handleExternalMessage);
    DaemonService.getInstance().stop();
});
</script>

<template>
    <router-view></router-view>
    <OaFloatNav />
</template>

<style lang="scss">
#CONTAINER-CC-TOOLBAR {
    width: 272px;
    bottom: 0;
    top: 0;
    left: 50%;
    transform: translateX(-50%);

    &.cc-toolbar {
        &.cc-toolbar-close {
            height: 64px;
        }
    }
}
</style>
