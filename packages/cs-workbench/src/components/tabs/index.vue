<script lang="ts" setup>
import { ref, onMounted, PropType } from 'vue';

const emit = defineEmits([
    'change',
]);

const props = defineProps({
    defaultValue: {
        type: String,
        default: '1',
    },
    list: {
        type: Array as PropType<any[]>,
        default: () => [],
    },
});

const activeName = ref('');

onMounted(() => {
    activeName.value = props.defaultValue || props.list[0]?.value || '';
});

function handleTabChange() {
    emit('change', activeName.value);
}

</script>
<template>
    <el-tabs
        v-model="activeName"
        class="components__tabs"
        @tab-change="handleTabChange"
    >
        <el-tab-pane
            v-for="item in props.list"
            :key="item.value"
            :name="item.value"
        >
            <template #label>
                <el-badge :value="item.count">{{ item.label }}</el-badge>
            </template>
        </el-tab-pane>
    </el-tabs>
</template>
<style lang="scss">
.components__tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__header {
        line-height: 56px;
        height: 56px;
        background-color: #fff;
        margin-bottom: 0;

        .el-tabs__nav-wrap::after {
            height: 0;
        }
    }

    .el-tabs__content {
        flex: 1;

        .el-tab-pane {
            height: 100%;
        }

        .item-wrapper {
            background-color: red;
            height: 30px;
            margin-bottom: 4px;
        }
    }

    .el-badge__content.is-fixed {
        top: 10px;
    }
}
</style>
