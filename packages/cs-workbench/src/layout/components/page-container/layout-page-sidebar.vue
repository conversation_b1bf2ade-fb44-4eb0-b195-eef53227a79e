<script setup lang="ts">
import { ref, PropType } from 'vue';

const emit = defineEmits([
    'search',
    'create',
    'item-click',
]);
const props = defineProps({
    header: {
        type: Boolean,
        default: true,
    },
    toolbar: {
        type: Boolean,
        default: true,
    },
    quickListTotal: {
        type: Number,
        default: 0,
    },
    quickList: {
        type: Array as PropType<any[]>,
        default: () => [],
    },
    searchPlaceholder: {
        type: String,
        default: '搜索',
    },
    needCreate: {
        type: Boolean,
        default: true,
    },
});

</script>

<template>
    <div class="layout-page-sidebar">
        <div v-if="$slots.header" class="layout-page-sidebar__header">
            <slot name="header"></slot>
        </div>

        <div class="layout-page-sidebar__main">
            <slot name="main"></slot>
            <slot></slot>
        </div>
    </div>
</template>

<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.layout-page-sidebar {
    height: 100%;
    flex-shrink: 0;

    @include mixins.flexLayout(flex-start, flex-start, column);

    &__header {
        width: 100%;
        //padding: 12px 20px;
        //height: var(--oa-pharmacy-header-height);
        border-bottom: var(--oa-pharmacy-border);
    }

    &__main {
        width: 100%;
        flex: 1;
        height: 0;

        @include mixins.flexLayout(flex-start, flex-start, column);
    }
}
</style>
