declare namespace  AbcAPI {
    
    type ClinicTicketMsgView = {    
        
        employeeId:string    
        
        employeeName:string    
        //客户信息：微信外部联系人id
        externalUserId:string    
        //客户信息：微信外部联系人昵称
        externalUserName:string    
        //消息id
        id:string    
        //文件消息{"md5sum":"18e93fc2ea884df23b3d2d3b8667b9f0","filename":"资料.docx","fileext":"docx","filesize":18181,"sdkfileid":"E4ODRkZjIzYjNkMmQzYjg2NjdiOWYwMDIwMTA1MDIwMTAwMDQwMBI4TkRkZk1UWTRPRGcxTURrek9UZzBPVEF6TTE4eE1EUXpOVGcxTlRVNVh6RTJNRE00TnpVMk1EZz0aIDMwMzkzMzY0NjEzNjM3NjY2NDY1NjMzNjYxMzIzNzYx"}
        msgFile?:MsgFile    
        //图片消息：https://developer.work.weixin.qq.com/document/path/91774#%E5%9B%BE%E7%89%87
        msgImage?:MsgImage    
        //混合消息
        msgMixed?:MsgMixed    
        //}
        msgText?:MsgText    
        //消息发送时间
        msgTime:string    
        //消息类型:text,image...{@linkClinicTicketMsg.MsgType}
        msgType:string    
        //视频消息：https://developer.work.weixin.qq.com/document/path/91774#%E8%AF%AD%E9%9F%B3
        msgVideo?:MsgVideo    
        //语音消息：https://developer.work.weixin.qq.com/document/path/91774#%E8%AF%AD%E9%9F%B3
        msgVoice?:MsgVoice    
        //消息来源：0客户1接待人
        origin:number    
        //接待人id
        servicerId:string    
        //接待人名字
        servicerName:string    
    }
}
