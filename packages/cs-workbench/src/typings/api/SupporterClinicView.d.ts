declare namespace  AbcAPI {
    
    type SupporterClinicView = {    
        //连锁全称
        chainFullName:string    
        //连锁id
        chainId:string    
        //连锁名称
        chainName:string    
        //远程设备信息
        clientDeviceViews?:Array<客户端设备信息>    
        //门店全称
        clinicFullName:string    
        //门店id
        clinicId:string    
        //门店名称
        clinicName:string    
        //创建人
        createdBy:number    
        //人员信息
        employee?:EmployeeA    
        //跟进记录id
        id:number    
        //最后更新时间
        lastModified:string    
        //待办ID
        todoId:string    
        //待办事项
        todoRemark:string    
        //待办事项更新时间
        todoUpdateTime:string    
    }
}
