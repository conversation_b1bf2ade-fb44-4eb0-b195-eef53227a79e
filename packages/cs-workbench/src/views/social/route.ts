/*
 * <AUTHOR>
 * @DateTime 2023-11-02 16:39:38
 */
import {
    PageFeature,
    PageClassify,
    PageExplain,
    PageRemote,
    PageLog,
    PageLogServe,
    PageChecking,
    PageVersion,
} from '@abc-oa/social';

export default [
    {
        path: 'social',
        name: '@social',
        component: () => import('./index.vue'),
        meta: {
            isEntry: true,
            name: '医保相关',
            icon: 'FirstAidKit',
        },
        redirect: {
            name: '@social/feature',
        },
        children: [
            {
                path: 'feature',
                name: '@social/feature',
                component: PageFeature,
                meta: {
                    name: '功能展示',
                    icon: 'iconoir:view-grid',
                    hidden: true,
                },
            },
            {
                path: 'classify',
                name: '@social/classify',
                component: PageClassify,
                meta: {
                    name: '错误归类',
                    icon: 'iconoir:archive',
                    hidden: true,
                },
            },
            {
                path: 'explain',
                name: '@social/explain',
                component: PageExplain,
                meta: {
                    name: '归类配文',
                    icon: 'iconoir:bed-ready',
                    hidden: true,
                },
            },
            {
                path: 'remote',
                name: '@social/remote',
                component: PageRemote,
                meta: {
                    name: '远程执行',
                    icon: 'iconoir:arrow-up-left-square',
                    hidden: true,
                },
            },
            {
                path: 'log',
                name: '@social/log',
                component: PageLog,
                meta: {
                    name: '前端日志',
                    icon: 'iconoir:page',
                    hidden: true,
                },
            },
            {
                path: 'log-serve',
                name: '@social/log-serve',
                component: PageLogServe,
                meta: {
                    name: '后端日志',
                    icon: 'iconoir:coins',
                    hidden: true,
                },
            },
            {
                path: 'checking',
                name: '@social/checking',
                component: PageChecking,
                meta: {
                    name: '对账统计',
                    icon: 'iconoir:kanban-board',
                    hidden: true,
                },
            },
            {
                path: 'version',
                name: '@social/version',
                component: PageVersion,
                meta: {
                    name: '版本监控',
                    icon: 'iconoir:reports',
                    hidden: true,
                },
            },
        ],
    },
];
