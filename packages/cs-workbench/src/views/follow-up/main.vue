<script setup lang="ts">
import { defineAsyncComponent, ref, markRaw } from 'vue';

import Tabs from '@/components/tabs/index.vue';
import { useLayoutTabsHook } from '@/components/tabs/controller.ts';
import LayoutPageMain from '@/layout/components/page-container/layout-page-main.vue';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';
import { useLayoutQuickListHook } from './hook/quick-list.ts';
import { useClinicTicketService } from '@/views/follow-up/hook/clinic-ticket-service.ts';

const { clinicTicketsProcessingCount } = useClinicTicketService();

// 使用 markRaw 包装异步组件
const createAsyncComponent = (loader: () => Promise<any>) => markRaw(defineAsyncComponent(loader));

const tabList = ref([
    { 
        value: '1', 
        label: '门店详情', 
        component: createAsyncComponent(() => import('./clinic-info/index.vue')),
    },
    { 
        value: '2', 
        label: '工单管理', 
        count: clinicTicketsProcessingCount, 
        component: createAsyncComponent(() => import('@/views/after-ticket/index.vue')),
    },
    { 
        value: '3', 
        label: '远程执行', 
        count: 0, 
        component: createAsyncComponent(() => import('./remote-script/index.vue')),
    },
    { 
        value: '4', 
        label: '追溯码统计', 
        count: 0, 
        component: createAsyncComponent(() => import('./traceability-code-statistics/index.vue')),
    },
    { 
        value: '5', 
        label: '咨询记录', 
        count: 0, 
        component: createAsyncComponent(() => import('@/views/follow-up/communication-records/index.vue')),
    },
    { 
        value: '6', 
        label: '备忘记录', 
        count: 0, 
        component: createAsyncComponent(() => import('@/views/follow-up/remark/record.vue')),
    },
    { 
        value: '7', 
        label: '常用操作', 
        count: 0, 
        component: createAsyncComponent(() => import('./common-operations/index.vue')),
    },
    // { value: '4', label: '成员列表', component: defineAsyncComponent(() => import('./employee-list/management.vue')) },
    // { value: '5', label: '医保日志' },
    // { value: '6', label: '操作记录' },
    // { value: '7', label: '咨询记录' },
]);

const { activeItem } = useLayoutQuickListHook();

console.log(activeItem, 'activeItem');

const {
    list,
    component,
    updateActiveName,
    updateTabList,
} = useLayoutTabsHook();

updateTabList(tabList.value);

</script>
<template>
    <layout-page-main>
        <template #header>
            <div style="padding: 0 20px;">
                <tabs
                    :list="list"
                    @change="updateActiveName"
                ></tabs>
            </div>
        </template>
        <div style="height: 100%;">
            <el-scrollbar>
                <layout-page-element :background="false">
                    <component :is="component" :clinic-id="activeItem?.clinicId" :is-clinic-dimension="true"></component>
                </layout-page-element>
            </el-scrollbar>
        </div>
    </layout-page-main>
</template>
