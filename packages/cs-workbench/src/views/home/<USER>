<script setup lang="ts">
import { onMounted } from 'vue';
// import { formatTicketStatus, formatTicketType } from '@/views/after-ticket/constant';
import { openExternal } from '@abc-oa/utils/src/electron';
import { useMyTicketService } from './hook/my-ticket-service.ts';
import ViewOperate from './components/view-operate.vue';

const {
    isLoading,
    currentPage,
    pageSize,
    totalRecords,
    tableDataList,
    fetchData,
} = useMyTicketService();

const handlePageChange = (page: number) => {
    currentPage.value = page;
    fetchData();
};

const linkJump = (row: any) => {
    if (row.tapdLick) {
        openExternal(row.tapdLick);
    }
};

onMounted(async () => {
    fetchData();
});

</script>

<template>
    <div v-loading="isLoading" style="height: 120px;">
        <view-operate
            v-for="item in tableDataList"
            :key="item.id"
            :show-data="item"
            :style="{ cursor: item.tapdLick ? 'pointer' : 'default' }"
            @click="linkJump(item)"
        ></view-operate>
    </div>
    <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="totalRecords"
        @current-change="handlePageChange"
    />
</template>

<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.my-ticket-list-table {
    .title {
        .el-link__inner {
            @include mixins.text-ellipsis(1);
        }
    }
}
</style>
