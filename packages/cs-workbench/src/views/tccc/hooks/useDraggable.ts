// `useDraggable.ts`
import { ref, onMounted, onBeforeUnmount } from 'vue';

export function useDraggable(id) {
    const isDragging = ref(false);
    let startX, startY;
    let dragTarget = null;
    let dragHandle = null;
    let dragHandleRight = null;

    const onMouseDown = (event) => {
        // console.log('onMouseDown', event);
        event.preventDefault();
        event.stopPropagation();

        isDragging.value = true;
        startX = event.clientX;
        startY = event.clientY;
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    };

    const onMouseMove = (event) => {
        if (!isDragging.value) return;
        const dx = event.clientX - startX;
        const dy = event.clientY - startY;
        dragTarget.style.left = `${dragTarget.offsetLeft + dx}px`;
        dragTarget.style.top = `${dragTarget.offsetTop + dy}px`;

        startX = event.clientX;
        startY = event.clientY;
    };

    const onMouseUp = (event) => {
        isDragging.value = false;
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
    };

    const observeToolbar = () => {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === id) {
                            // 左边增加一个拖动
                            dragHandle = document.createElement('div');
                            dragHandle.style.position = 'absolute';
                            dragHandle.style.top = '0';
                            dragHandle.style.left = '0';
                            dragHandle.style.width = '100px';
                            dragHandle.style.height = '64px';
                            dragHandle.style.backgroundColor = 'transparent';
                            dragHandle.style.zIndex = '9999';
                            dragHandle.style.cursor = 'move';

                            // 右边增加一个拖动
                            dragHandleRight = document.createElement('div');
                            dragHandleRight.style.position = 'absolute';
                            dragHandleRight.style.top = '0';
                            dragHandleRight.style.right = '0';
                            dragHandleRight.style.width = '100px';
                            dragHandleRight.style.height = '64px';
                            dragHandleRight.style.backgroundColor = 'transparent';
                            dragHandleRight.style.zIndex = '9999';
                            dragHandleRight.style.cursor = 'move';

                            node.appendChild(dragHandle);
                            node.appendChild(dragHandleRight);
                            dragHandle.addEventListener('mousedown', onMouseDown);
                            dragHandleRight.addEventListener('mousedown', onMouseDown);

                            dragTarget = node;
                        }
                    });
                    mutation.removedNodes.forEach((node) => {
                        if (node.id === id) {
                            dragHandle.removeEventListener('mousedown', onMouseDown);
                            dragHandleRight.removeEventListener('mousedown', onMouseDown);
                        }
                    });
                }
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });

        onBeforeUnmount(() => {
            observer.disconnect();
        });
    };

    onMounted(() => {
        observeToolbar();
    });
}
