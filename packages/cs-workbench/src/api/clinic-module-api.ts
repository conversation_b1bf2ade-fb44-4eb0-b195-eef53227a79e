import BaseAPI from './base-api';

/**
* 
*/
export class ClinicModuleApi extends BaseAPI {
    /**
    * 获取诊所分类ModuleId列表
    * @param {string} parentId -     
    */
    static getApiLowCodeClinicModuleList(
        parentId:string,
    ) {
        return this.get<Array<AbcAPI.V2ClinicModule>>('/api/low-code/clinic-module/list', {
            params: {
                parentId,
            },
        });
    }
    
    /**
    * 获取打印引擎配置
    * @param {string} chainId -     
    * @param {string} clinicId -     
    */
    static getApiLowCodeClinicModulePrintConfig(
        chainId:string,
        clinicId:string,
    ) {
        return this.get<object>('/api/low-code/clinic-module/print-config', {
            params: {
                chainId,
                clinicId,
            },
        });
    }
}