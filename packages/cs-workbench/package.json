{"name": "cs-workbench", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --mode loc", "build": "node ./build.cjs", "preview": "vite preview", "swagger": "node ./tools/swagger2ts.js && npx eslint --fix ./src/api/*.ts", "create:page": "plop --plopfile ./tools/generators/page.js"}, "dependencies": {"@abc-oa/common": "workspace:*", "@abc-oa/components": "workspace:*", "@abc-oa/social": "workspace:*", "@abc-oa/styles": "workspace:*", "@abc-oa/tools": "workspace:*", "@abc-oa/utils": "workspace:*", "@tiptap/core": "2.1.12", "@tiptap/extension-color": "2.1.12", "@tiptap/extension-font-family": "2.1.12", "@tiptap/extension-highlight": "2.1.12", "@tiptap/extension-horizontal-rule": "2.1.12", "@tiptap/extension-image": "~2.1.13", "@tiptap/extension-link": "2.1.12", "@tiptap/extension-subscript": "2.1.12", "@tiptap/extension-superscript": "2.1.12", "@tiptap/extension-text-align": "2.1.12", "@tiptap/extension-text-style": "2.1.12", "@tiptap/extension-typography": "2.1.12", "@tiptap/extension-underline": "2.1.12", "@tiptap/pm": "2.1.13", "@tiptap/starter-kit": "2.1.12", "@tiptap/vue-3": "2.1.12", "abc-oa-auth-sdk": "^0.0.6", "chart.js": "^4.4.9", "dayjs": "~1.10.8", "element-plus": "2.2.6", "pinia": "~2.0.8", "prosemirror-dev-toolkit": "~1.1.5", "remixicon": "~3.5.0", "tencentcloud-sdk-nodejs": "^4.0.966", "vant": "3.3.7", "vue": "~3.2.25", "vue-codemirror": "~6.1.1", "vue-json-viewer": "3", "vue-router": "4.0.12", "vuedraggable": "~4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "abc-fed-build-tool": "0.5.0", "typescript": "^5.5.3", "vite": "5.4.11", "vue-tsc": "^2.1.6"}}