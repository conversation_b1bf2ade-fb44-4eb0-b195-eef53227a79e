server {
    listen 9090;
    root /workspace/www;

    access_log /workspace/log/access.log;
    error_log   /workspace/log/error.log;

    location ~ \.(jpg|png|jpeg|gif)$ {
        expires 30d;
    }

    location ~ \.(js|css)$ {
        expires 1d;
    }

    location / {
        if (-f $request_filename){
          break;
        }

        add_header Cache-Control no-store;

        rewrite /* /index.html break;
    }
}
