<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';
import { DataTransferApi } from '@/api/data-transfer-api.ts';

const dataList = ref([]);
const currentPage = ref(1);
const pageSize = ref(15);
const total = ref(0);
const loading = ref(false);

const fetchData = async () => {
    loading.value = true;
    try {
        const offset = (currentPage.value - 1) * pageSize.value;
        let response = {
            rows: [
                {
                    name: '示例数据1',
                    downloadUrl: 'https://www.abcyun.cn',
                },
                {
                    name: '示例数据2',
                    downloadUrl: 'https://www.abcyun.cn',
                },
            ],
            total: 2,
        };
        dataList.value = response.rows || [];
        total.value = response.total;
    } catch (error) {
        ElMessage.error('获取模板数据失败');
    } finally {
        loading.value = false;
    }
};

const handlePageChange = (page: number) => {
    currentPage.value = page;
    fetchData();
};

onMounted(() => {
    fetchData();
});

</script>

<template>
    <layout-page-element>
        <el-scrollbar>
            <el-table
                v-loading="loading"
                border
                :data="dataList"
                style="width: 100%;"
            >
                <el-table-column prop="name" label="示例名称">

                </el-table-column>
                <el-table-column prop="downloadUrl" label="操作" width="100px">
                    <template #default="{ row }">
                        <el-link type="primary" :href="row.downloadUrl" target="_blank">下载</el-link>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                :current-page="currentPage"
                :page-size="pageSize"
                layout="total, prev, pager, next"
                :total="total"
                @current-change="handlePageChange"
            />
        </el-scrollbar>
    </layout-page-element>
</template>
