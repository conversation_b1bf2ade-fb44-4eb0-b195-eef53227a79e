import { RouteRecordRaw } from 'vue-router';

const Index = () => import('./index.vue');

export default [
    {
        path: '/{{path}}',
        name: '@{{pageName}}',
        component: Index,
        meta: {
            isEntry: true,
            name: '{{pageNameStr}}',
            icon: 'ix:item-details',
            // 值越大，排序越靠后
            sort: 300,
        },
        redirect: {
            name: '@{{pageName}}/child',
        },
        children: [
            {
                path: 'child',
                name: '@{{pageName}}/child',
                component: () => import('./child/index.vue'),
                meta: {
                    name: '二级菜单',
                    icon: 'uil:subject',
                    hidden: true,
                },
                redirect: {
                    name: '@{{pageName}}/child-sub',
                },
                children: [
                    {
                        path: 'sub',
                        name: '@{{pageName}}/child-sub',
                        component: () => import('./child/sub/index.vue'),
                        meta: {
                            name: '三级菜单',
                        },
                    },
                ],
            },
        ],
    },
] as RouteRecordRaw[];
