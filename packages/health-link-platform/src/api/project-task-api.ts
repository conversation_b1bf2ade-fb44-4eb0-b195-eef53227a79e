/*
 * <AUTHOR> 
 * @DateTime 2025-09-08 19:40:32 
 */
import BaseApi from './base-api';
import * as utils from '@/common/utils';

export default class ProjectTaskApi extends BaseApi {
    /**
     * 根据项目ID获取任务列表
     * <AUTHOR>
     * @date 2025-09-08
     * @param {string} projectId
     * @returns {Promise<AbcResponse>}
     */
    static fetchProjectTaskList(projectId: string) {
        return this.fetchPack({
            url: `/api/management/healthlink/project-task/by-project-id/${projectId}`,
            method: 'GET',
        });
    }

    /**
     * 创建项目任务
     * <AUTHOR>
     * @date 2025-09-08
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static createProjectTask(data: any) {
        return this.fetchPack({
            url: '/api/management/healthlink/project-task',
            method: 'POST',
            data,
            isUnableRequest: true,
        });
    }
    
    /**
     * 更新项目任务
     * <AUTHOR>
     * @date 2025-09-08
     * @param {string} id
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static updateProjectTask(id: string, data: any) {
        return this.fetchPack({
            url: `/api/management/healthlink/project-task/${id}`,
            method: 'PUT',
            data,
            isUnableRequest: true,
        });
    }
    
    /**
     * 删除项目任务
     * <AUTHOR>
     * @date 2025-09-08
     * @param {string} id
     * @returns {Promise<AbcResponse>}
     */
    static deleteProjectTask(id: string) {
        return this.fetchPack({
            url: `/api/management/healthlink/project-task/${id}`,
            method: 'DELETE',
            isUnableRequest: true,
        });
    }

    /**
     * 获取数据源配置
     * <AUTHOR>
     * @date 2025-09-11
     * @returns {Promise<AbcResponse>}
     */
    static fetchDataSourceConfig() {
        return this.fetchPack({
            url: '/api/management/healthlink/datasource',
            method: 'GET',
        });
    }
    
    /**
     * 请求数据转换测试
     * <AUTHOR>
     * @date 2025-09-11
     * @param {Object} data
     * @returns {Promise<AbcResponse>}
     */
    static requestJsonTransformer(data: any) {
        return this.fetchPack({
            url: '/api/management/healthlink/json-transformer/transform',
            method: 'POST',
            data,
        });
    }
    
    /**
     * 智能添加
     * <AUTHOR>
     * @date 2025-09-11
     * @param {Object} data
     * @param {Function} callback
     * @returns {Promise<AbcResponse>}
     */
    static fetchAiIntelligentAdd(data: any, callback: any) {
        return this.fetchPack({
            url: '/api/management/healthlink/ai/analysis/text/healthlink-protocol-json-transform',
            method: 'POST',
            data,
            isUnableRequest: true,
            responseType: 'stream', // 关键：指定响应类型为流
            timeout: 1000 * 60 * 10, // 10分钟
            onDownloadProgress: (progressEvent: any) => {
                const result = progressEvent.target.responseText
                                .split('\n')
                                .filter((item: string) => item.trim() !== '')
                                .reduce((result: string, item: string) => {
                                    const dataStr = item.replace(/^data:/, '');
                                    if (dataStr === '[DONE]') {
                                        return result;
                                    }
                                    const dataObj = utils.jsonParseSafety(dataStr);
                                    if (!dataObj) {
                                        return result;
                                    }
                                    const thinking = dataObj.data?.thinking || '';
                                    if (thinking) {
                                        result += thinking;
                                    }
                                    const answer = dataObj.data?.answer || '';
                                    if (answer) {
                                        const flag = '==========以下为输出结果==========';
                                        if (result.includes(flag) === false) {
                                            result += `\n${flag}\n`;
                                        }
                                        result += answer;
                                    }
                                    return result;
                                }, '');
                callback(result);
            },
        });
    }
}