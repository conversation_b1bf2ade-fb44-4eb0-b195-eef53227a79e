@import "./media/var";

:root {
    --el-menu-bg-color: var(--app-bg-color);
}
// 侧边栏
.el-aside {
    //transition: width .3s;
}

.el-overlay-dialog {
    display: flex;
    align-items: center;

    .el-dialog {
        margin: 0 auto;
    }
}

// 顶部菜单
.el-header {
    --el-text-color-regular: var(--app-text-color-regular-dark);
    --el-text-color-primary: var(--app-text-color-primary-dark);

    height: 48px;
    color: var(--app-text-color-regular-dark);
    background-color: var(--app-bg-color);
}

// 菜单
.el-menu {
    --el-menu-border-color: transparent;
    --el-menu-hover-bg-color: rgba(255, 255, 255, .15);
    --el-menu-text-color: var(--app-text-color-regular-dark);
    --el-menu-active-color: var(--app-text-color-primary-dark);
    --el-menu-item-height: 36px;

    .el-menu-item {
        border-radius: var(--oa-border-radius-4);

        &.is-active {
            background: rgba(255, 255, 255, .15);
        }
    }
}

// 卡片
.el-card {
    --el-card-padding: var(--oa-padding-12);

    border: none !important;
}

.el-avatar {
    --el-avatar-text-font-size: 20px;
}

.van-cell {
    --van-cell-horizontal-padding: var(--oa-padding-12);
}

.van-cell__value {
    flex: 2 !important;
}

.el-message {
    min-width: 100px;
}

/* 页面宽度小于576px
------------------------------- */
@media screen and (max-width: $xs) {
    .el-header {
        --el-header-height: 48px;
    }

    .el-main {
        --el-main-padding: var(--oa-padding-12);
    }
}
