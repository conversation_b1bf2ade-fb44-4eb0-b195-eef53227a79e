* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    outline: none !important;
}

:root {
    --app-bg-color: linear-gradient(252deg, #5078c8, #578ecf 20%, #578ecf 80%, #5078c8);
    --app-text-color-primary-dark: rgba(255, 255, 255, 1);
    --app-text-color-regular-dark: rgba(255, 255, 255, .8);
}

html,
body,
#app {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    font-family: var(--oa-base-font-family);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -webkit-tap-highlight-color: transparent;
    background: #122037;
    font-size: var(--oa-font-size-14);
    overflow: hidden;
    position: relative;
    color: var(--oa-text-color);
}

::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
}

[v-clock] {
    display: none;
}

// 布局样式
.layout-h5-container {
    width: 100%;
    height: 100%;
    overflow: auto;

    .layout-page-wrapper {
        padding: var(--oa-page-padding);
    }

    .layout-tabbar-page-wrapper {
        padding: var(--oa-page-padding) var(--oa-page-padding) 0;

        .layout-tabbar-page__content {
            height: calc(100vh - 58px);
            overflow: auto;
        }

        .layout-tabbar-page__tabbar {
            padding-bottom: 8px;
        }
    }
}

#app .layout-pc-container {
    --app-layout-header-height: 48px;

    width: 100%;
    height: 100%;

    .layout-aside {
        background-color: var(--app-bg-color);
        height: 100vh;
        transition: width .3s;
        padding: 0 var(--oa-padding-12) var(--oa-padding-12) var(--oa-padding-12);
        display: flex;
        flex-direction: column;

        &.layout-aside__open {
            width: 180px;
        }

        &.layout-aside__close {
            width: 64px;

            .layout-aside__brand {
                .brand-name {
                    display: none;
                }
            }
        }

        // 移动端适配
        &.layout-aside-mobile {
            position: fixed;
            top: 0;
            width: 180px;
            z-index: 99999;
            left: -180px;
            transition: all .3s ease-out;

            &.layout-aside__open {
                left: 0;
            }

            &.layout-aside__close {
                left: -180px;
            }
        }

        .layout-aside__brand {
            height: var(--app-layout-header-height);
            font-size: var(--oa-font-size-16);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: var(--app-text-color-regular-dark);

            @include text-no-wrap;

            .brand-img {
                width: 20px;
                height: 20px;
            }
        }

        .layout-aside__divider {
            background: linear-gradient(270deg, rgba(0, 0, 0, .02), rgba(0, 0, 0, .1) 50%, rgba(0, 0, 0, .04));
            height: 1px;
            margin-bottom: 10px;
            width: 100%;
        }

        .layout-aside__menu {
            flex: 1;
            width: 100%;
            overflow: auto;

            &:not(.el-menu--collapse) {
                //width: 180px;
                min-height: 400px;
            }

            &.el-menu--collapse {
                .el-menu-item {
                    padding: 8px;
                }
            }

            .el-menu-item + .el-menu-item {
                margin-top: 8px;
            }
        }
    }

    .layout-header-wrapper {
        height: var(--app-layout-header-height);

        .layout-header__menu-toggle {
            margin-right: 12px;
            font-size: 18px;
            cursor: pointer;
        }

        .layout-header__right {
            margin-left: auto;

            .layout-header__user-info {
                display: flex;
                align-items: center;
                margin-left: auto;

                .user-name {
                    margin-left: 6px;
                }
            }
        }
    }

    .layout-tabbar-page-wrapper {
        &.layout-tabbar-page__sidebar--open {
            .layout-tabbar-page__tabbar,
            .h5-form__action-wrapper,
            .pc-form__action-wrapper {
                transition: all .3s ease-out;
                width: calc(100% - 180px);
                left: 180px;
            }
        }

        &.layout-tabbar-page__sidebar--close {
            .layout-tabbar-page__tabbar,
            .h5-form__action-wrapper,
            .pc-form__action-wrapper {
                transition: all .3s ease-out;
                width: calc(100% - 64px);
                left: 64px;
            }
        }
    }
}
