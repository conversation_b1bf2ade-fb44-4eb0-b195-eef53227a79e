<template>
    <div class="view-project">
        <template v-if="isShowProject">
            <div class="tools-warpper">
                <el-input
                    v-model="toolsParams.keyword"
                    class="search-input"
                    :suffix-icon="Search"
                    clearable
                    placeholder="Key / 项目名称 / 项目描述"
                />
                <div class="track-box"></div>
                <el-button type="primary" @click="onClickCreateProject">新建项目</el-button>
            </div>
            <div class="content-wrapper">
                <el-table
                    v-loading="loadingModelData.loading.value"
                    :data="showDataList"
                    :height="530"
                    style="width: 100%;"
                >
                    <el-table-column
                        prop="name"
                        label="项目Key"
                        min-width="200"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="displayName"
                        label="项目名称"
                        min-width="200"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="description"
                        label="项目描述"
                        min-width="260"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="docUrl"
                        label="在线文档"
                        min-width="260"
                    >
                        <template #default="scope">
                            <el-link
                                v-if="scope.row.docUrl"
                                :href="scope.row.docUrl"
                                target="_blank"
                                underline="never"
                            >
                                <el-text class="link-text" truncated>
                                    {{ scope.row.docUrl }}
                                </el-text>    
                            </el-link>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="reportEndpointWording"
                        label="上报类型"
                        min-width="140"
                    />
                    <el-table-column
                        prop="isValidWording"
                        label="是否启用"
                        min-width="140"
                    >
                        <template #default="scope">
                            <div class="align-center">
                                <span class="text-zhan">{{ scope.row.isValidWording }}</span>
                                <el-button
                                    v-if="scope.row.isValid === 0"
                                    type="text"
                                    style="margin-left: 8px; transform: translateY(.5px);"
                                    @click="onClickEnable(scope.row)"
                                >
                                    启用
                                </el-button>
                                <el-button
                                    v-if="scope.row.isValid === 1"
                                    type="text"
                                    style="margin-left: 8px; transform: translateY(.5px);"
                                    @click="onClickDisable(scope.row)"
                                >
                                    禁用
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="taskCount"
                        label="任务数"
                        min-width="140"
                    >
                        <template #default="scope">
                            <div class="align-center">
                                <span class="text-zhan">{{ scope.row.taskCount }}</span>
                                <el-button
                                    type="text"
                                    style="margin-left: 8px; transform: translateY(.5px);"
                                    @click="onClickTasks(scope.row)"
                                >
                                    管理
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="lastModifiedBy"
                        label="创建人"
                        min-width="140"
                    />
                    <el-table-column
                        prop="lastModifiedWording"
                        label="创建时间"
                        min-width="180"
                    />
                    <el-table-column
                        prop="remark"
                        label="操作"
                        width="180"
                        fixed="right"
                    >
                        <template #default="scope">
                            <div class="align-center">
                                <el-button
                                    plain
                                    type="primary"
                                    @click="onClickEdit(scope.row)"
                                >
                                    编辑
                                </el-button>
                                <el-button
                                    plain
                                    type="danger"
                                    @click="onClickDelete(scope.row)"
                                >
                                    删除
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="footer-box">
                    <el-pagination
                        v-model:current-page="pageModel.params.page"
                        :page-size="pageModel.params.pageSize"
                        :total="pageModel.params.total"
                        background
                        layout="total, prev, pager, next"
                    />
                </div>
            </div>

            <dialog-project-create
                v-if="dialogProjectCreateMode.visible.value"
                v-model="dialogProjectCreateMode.visible.value"
                :edit-item="projectData.editItem"
                @update="updateProjectData"
            />
        </template>
        <router-view v-else></router-view>
    </div>
</template>

<script setup lang="ts">
import DialogProjectCreate from './dialog-project-create/index.vue';
import * as utils from '@/common/utils';
import { computed, onMounted } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useRoute, useRouter } from 'vue-router';
import { createProjectController } from './controller';

const route = useRoute();
const router = useRouter();

const isShowProject = computed(() => route.name === '@project');

const {
    dialogProjectCreateMode,
    loadingModelData,
    pageModel,
    projectData,
    setEditItem,
    delEditItem,
    toolsParams,
    showDataList,
    updateProjectData,
    requestProjectDataList,
    requestDeleteProject,
    requestEnableProject,
    requestDisableProject,
} = createProjectController();

onMounted(() => {
    fetchProjectDataList();
});

/**
 * 获取项目列表
 * <AUTHOR>
 * @date 2025-09-09
 */
const fetchProjectDataList = async () => {
    loadingModelData.setLoading(true);
    const fetchResponse = await requestProjectDataList();
    loadingModelData.setLoading(false);
    if (fetchResponse.status === false) {
        ElMessage.error(fetchResponse.message);
        return;
    }
    projectData.dataList = fetchResponse.data?.rows || [];
};

/**
 * 新建项目
 * <AUTHOR>
 * @date 2025-09-03
 */
const onClickCreateProject = () => {
    delEditItem();
    dialogProjectCreateMode.show();
};

/**
 * 启用项目
 * <AUTHOR>
 * @date 2025-09-08
 * @param {Object} row
 */
const onClickEnable = async (row: any) => {
    const confirmResponse = await utils.messageConfirm('启用后将开始数据上报，请确定是否启用？', '提示', {
        type: 'warning',
        confirmButtonText: '启用',
        cancelButtonText: '取消',
    });
    if (confirmResponse.status === false) {
        return confirmResponse;
    }
    const enableResponse = await requestEnableProject(row.id);
    if (enableResponse.status === false) {
        ElMessage.error(enableResponse.message);
        return;
    }
    ElMessage.success('启用成功');
    fetchProjectDataList();
};    

/**
 * 禁用项目
 * <AUTHOR>
 * @date 2025-09-08
 * @param {Object} row
 */
const onClickDisable = async (row: any) => {
    const confirmResponse = await utils.messageConfirm('禁用后将停止数据上报，请确定是否禁用？', '提示', {
        type: 'warning',
        confirmButtonText: '禁用',
        cancelButtonText: '取消',
    });
    if (confirmResponse.status === false) {
        return confirmResponse;
    }
    const disableResponse = await requestDisableProject(row.id);
    if (disableResponse.status === false) {
        ElMessage.error(disableResponse.message);
        return;
    }
    ElMessage.success('禁用成功');
    fetchProjectDataList();
};    

/**
 * 查看任务
 * <AUTHOR>
 * @date 2025-09-03
 */
const onClickTasks = (row: any) => {
    router.push({
        name: '@project-task',
        params: {
            projectId: row.id,
        },
    });
};

/**
 * 编辑项目
 * <AUTHOR>
 * @date 2025-09-03
 */
const onClickEdit = (row: any) => {
    setEditItem(row);
    dialogProjectCreateMode.show();
};

/**
 * 删除项目
 * <AUTHOR>
 * @date 2025-09-03
 */
const onClickDelete = async (row: any) => {
    const confirmResponse = await utils.messageConfirm('删除后将停止数据上报，请确定是否删除？', '提示', {
        type: 'warning',
        confirmButtonText: '删除',
        cancelButtonText: '取消',
    });
    if (confirmResponse.status === false) {
        return confirmResponse;
    }
    const deleteResponse = await requestDeleteProject(row.id);
    if (deleteResponse.status === false) {
        ElMessage.error(deleteResponse.message);
        return;
    }
    ElMessage.success('删除成功');
    fetchProjectDataList();
};
</script>

<style lang="scss">
.view-project {
    .tools-warpper {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 8px;

        .track-box {
            flex: 1;
        }

        .search-input {
            width: 240px;
        }
    }

    .content-wrapper {
        margin-top: 16px;

        .align-center {
            display: flex;
            align-items: center;

            .text-zhan {
                display: inline-block;
                min-width: 24px;
            }
        }

        .el-table__empty-text {
            margin-top: 150px;
        }

        .link-text {
            max-width: 260px;
            font-weight: 400;

            &:hover {
                color: #409eff !important;
            }
        }

        .footer-box {
            margin-top: 16px;
            display: flex;
            justify-content: flex-end;
        }
    }
}
</style>
