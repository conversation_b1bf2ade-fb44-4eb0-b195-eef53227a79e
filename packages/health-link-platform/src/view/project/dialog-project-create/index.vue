<template>
    <el-dialog
        v-model="isShowDialogProjectCreate"
        :title="title"
        width="1050px"
        align-center
        append-to-body
        :close-on-click-modal="false"
        body-class="view-project__dialog-project-create_body"
    >
        <el-form
            :model="formData"
            label-width="72px"
        >
            <div class="row-box">
                <div class="left-wrapper">
                    <h3>基础信息</h3>
                    <el-form-item label="项目Key">
                        <el-input
                            v-model="formData.name"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="项目名称">
                        <el-input
                            v-model="formData.displayName"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="项目描述">
                        <el-input
                            v-model="formData.description"
                            type="textarea"
                            :rows="5"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="文档地址" style="margin-bottom: 0;">
                        <el-input
                            v-model="formData.docUrl"
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                </div>
                <div class="line-wrapper"></div>
                <div class="right-wrapper">
                    <h3>上报配置</h3>
                    <el-form-item label="上报类型">
                        <el-radio-group v-model="formData.reportEndpoint">
                            <el-radio :label="0">前端上报</el-radio>
                            <el-radio :label="1">后端上报</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="初始数据">
                        <el-radio-group
                            v-model="formData.initialReportTime.type"
                            class="column-wrapper"
                        >
                            <div class="column-item">
                                <el-radio label="fix">固定时间</el-radio>
                                <template v-if="formData.initialReportTime.type === 'fix'">
                                    <span class="desc">将上报</span>
                                    <el-date-picker
                                        v-model="formData.initialReportTime.startTime"
                                        type="date"
                                        value-format="YYYY-MM-DD"
                                        placeholder="选择日期"
                                        style="width: 140px;"
                                    ></el-date-picker>
                                    <span class="desc">当日及之后的数据</span>
                                </template>
                            </div>
                            <div class="column-item">
                                <el-radio label="relativeToOpenTime">相对时间</el-radio>
                                <template v-if="formData.initialReportTime.type === 'relativeToOpenTime'">
                                    <span class="desc">将上报开通日期前</span>
                                    <el-input
                                        v-model="formData.initialReportTime.relativeTimeInDays"
                                        style="width: 46px;"
                                    ></el-input>
                                    <span class="desc">天的数据</span>
                                </template>
                            </div>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="上报频率">
                        <el-radio-group
                            v-model="formData.reportFreq.type"
                            class="column-wrapper"
                        >
                            <div class="column-item">
                                <el-radio label="poll">轮询</el-radio>
                                <template v-if="formData.reportFreq.type === 'poll'">
                                    <span class="desc">每隔</span>
                                    <el-input
                                        v-model="formData.reportFreq.intervalInSeconds"
                                        :formatter="(value: string) => value.replace(/[^0-9]/g, '')"
                                        style="width: 120px;"
                                    >
                                        <template #append>
                                            <el-select
                                                v-model="formData.reportFreq.intervalUnit"
                                                style="width: 72px;"
                                            >
                                                <el-option value="s" label="秒" />
                                                <el-option value="m" label="分钟" />
                                                <el-option value="h" label="小时" />
                                            </el-select>
                                        </template>
                                    </el-input>
                                    <span class="desc">上报</span>
                                </template>
                            </div>
                            <div class="column-item">
                                <el-radio label="t1">T+1</el-radio>
                            </div>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="上报时间" style="margin-bottom: 0;">
                        <div class="timer-wrapper">
                            <el-time-picker
                                v-for="index in formData.reportAllowTimeRange.length"
                                :key="index"
                                v-model="formData.reportAllowTimeRange[index - 1]"
                                is-range
                                range-separator="至"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                value-format="HH:mm"
                                style="max-width: 200px;"
                            />
                        </div>
                    </el-form-item>
                </div>
            </div>
            <div class="row-box" style="margin-top: 20px;">
                <div class="left-wrapper">
                    <h3>请求配置</h3>
                    <el-form-item label="Protocol">
                        <el-radio-group
                            v-model="formData.protocol"
                            @change="onChangeProtocol"
                        >
                            <el-radio label="http">HTTP</el-radio>
                            <el-radio label="https">HTTPS</el-radio>
                            <el-radio label="custom">自定义</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <template v-if="formData.protocol === 'http' || formData.protocol === 'https'">
                        <el-form-item label="Hostname">
                            <el-input
                                v-model="formData.hostname"
                                placeholder="请输入"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="Port">
                            <el-input
                                v-model="formData.port"
                                type="number"
                                min="1"
                                placeholder="请输入"
                                style="width: 180px;"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="Path">
                            <el-input
                                v-model="formData.path"
                                placeholder="请输入"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="Method">
                            <el-radio-group v-model="formData.method">
                                <el-radio label="get">GET</el-radio>
                                <el-radio label="post">POST</el-radio>
                                <el-radio label="put">PUT</el-radio>
                                <el-radio label="delete">DELETE</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="Timeout">
                            <el-input
                                v-model="formData.timeout"
                                type="number"
                                min="1"
                                placeholder="请输入"
                                style="width: 180px;"
                            >
                                <template #append>秒</template>
                            </el-input>
                        </el-form-item>
                    </template>
                </div>
                <div class="line-wrapper"></div>
                <div class="right-wrapper"></div>
            </div>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    type="primary"
                    :disabled="isDisabledSaveBtn"
                    :loading="loadingModel.loading.value"
                    @click="onClickSubmit"
                >
                    提交
                </el-button>
                <el-button
                    @click="onClickCancel"
                >
                    取消
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { createProjectCreateController } from './controller';
import { computed, onMounted } from 'vue';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    editItem: {
        type: Object,
        default: null,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'update', // 更新
]);

const isShowDialogProjectCreate = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModel,
    isDisabledSaveBtn,
    title,
    formData,
    initFormData,
    initReportAllowTimeRange,
    setCachePostData,
    createPostData,
    requestSubmit,
} = createProjectCreateController(props);

onMounted(() => {
    initFormData();
    initReportAllowTimeRange();
    const postData = createPostData();
    setCachePostData(postData);
});

/**
 * 当协议改变时触发
 * <AUTHOR>
 * @date 2025-10-09
 */
const onChangeProtocol = () => {
    if (formData.protocol === 'http') {
        formData.port = 80;
    }
    if (formData.protocol === 'https') {
        formData.port = 443;
    }
};

/**
 * 当点击保存时触发
 * <AUTHOR>
 * @date 2025-09-03
 */
const onClickSubmit = async () => {
    loadingModel.setLoading(true);
    const createResponse = await requestSubmit();
    loadingModel.setLoading(false);
    if (createResponse.status === false) {
        ElMessage.error(createResponse.message);
        return;
    }
    ElMessage.success('创建成功');
    isShowDialogProjectCreate.value = false;
    $emit('update', createResponse.data);
};

/**
 * 当点击取消时触发
 * <AUTHOR>
 * @date 2025-09-03
 */
const onClickCancel = () => {
    isShowDialogProjectCreate.value = false;
};
</script>

<style lang="scss">
    .view-project__dialog-project-create_body {
        .el-form {
            .row-box {
                display: flex;
                flex-direction: row;
                align-items: stretch;

                h3 {
                    margin-bottom: 20px;
                }

                .left-wrapper {
                    flex: 1;

                    & > .el-form-item:last-child {
                        margin-bottom: 0;
                    }
                }

                .line-wrapper {
                    width: 1px;
                    background-color: #dcdfe6;
                    margin: 0 16px;
                }

                .right-wrapper {
                    flex: 1;

                    & > .el-form-item:last-child {
                        margin-bottom: 0;
                    }
                }

                .column-wrapper {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;

                    .column-item {
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;

                        label.el-radio {
                            margin-right: 12px;
                        }

                        .desc {
                            font-size: 14px;
                            margin: 0 8px;
                        }
                    }
                }

                .timer-wrapper {
                    display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    align-items: flex-start;

                    .el-date-editor {
                        margin-bottom: 6px;
                        margin-right: 6px;
                    }

                    & > .el-date-editor:last-child {
                        margin: 0;
                    }

                    & > .el-date-editor:nth-child(2n) {
                        margin-right: 0;
                    }
                }
            }
        }

        textarea {
            resize: none !important;
        }
    }
</style>