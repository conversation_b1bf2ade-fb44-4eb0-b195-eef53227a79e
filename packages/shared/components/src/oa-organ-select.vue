<script setup lang="ts" >
import { computed, ref, nextTick, onMounted, onBeforeUnmount } from 'vue';
import {
    cloudFunction,
    vendorModel,
    getClinicTypeDisplayName,
    getHisTypeDisplayName,
    NodeTypeClinic,
    isChainSubClinic,
    HisTypeClinic,
    concatAddress,
} from '@abc-oa/common';
import { ElMessage } from 'element-plus';
import _ from 'lodash';

const props = defineProps({
    modelValue: {
        type: [String, Array],
        default: '',
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: '请选择门店',
    },
    nodeTypeFilter: {
        type: Number,
        default: vendorModel.NodeTypeFilter.NO_FILTER,
    },
    showChain: {
        type: Boolean,
        default: false,
    },
    showAddress: {
        type: Boolean,
        default: false,
    },
    showAdministrator: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:modelValue', 'change']);
const organValue = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
const loading = ref(false);
const organList = ref<any[]>([]);
const selectRef = ref<any>(null);
const assistantInputRef = ref<HTMLInputElement | null>(null);
const isIOS = ref(false);
const focusTimerId = ref<number | null>(null);

/**
 * @description: 获取机构列表
 * @date: 2024-06-12 15:35:28
 * @author: Horace
 * @param {string} keyword 关键字
 * @return
 */
async function fetchOrganList(keyword: string) {
    if (!keyword || keyword.length < 2) {
        return [];
    }
    let res: any = {};
    const params = {
        keyword,
        nodeTypeFilter: props.nodeTypeFilter,
        isTrial: 0,
    };
    loading.value = true;
    try {
        res = await cloudFunction.OrganListFunction.exec(params);
    } catch (e: any) {
        console.error(e.message || e);
        ElMessage.error(e.message || e);
    } finally {
        loading.value = false;
    }
    if (!res.status) {
        console.error('getOrganList Response', res.message);
        ElMessage.error(res.message || res);
    }
    return res.data?.rows?.map((item: any) => ({
        ...item,
        label: item.name,
        value: item.id,
    })) || [];
}
const _debounceSearch = _.debounce(async (keyword: string) => {
    organList.value = await fetchOrganList(keyword);
}, 500);
function handleClinicIdsChange(values: string | string[]) {
    if (props.multiple) {
        const selectedClinics = organList.value.filter((item: any) => (values as string[]).includes(item.value));
        emit('change', values, selectedClinics);
    } else {
        const selectedClinic = organList.value.find((item: any) => item.value === values);
        emit('change', values, selectedClinic);
    }
}

function displayOrgan(organ: any) {
    return {
        name: organ.shortName || organ.name,
        address: concatAddress(organ),
        adminName: organ.adminName,
        adminMobile: organ.adminMobile,
        clinicTypeName: getClinicTypeDisplayName(<NodeTypeClinic>organ),
        hisTypeName: getHisTypeDisplayName(<HisTypeClinic>organ),
        chainName: isChainSubClinic(<any>organ) ? organ.chainName : '',
    };
}

// 判断是否为iOS设备
onMounted(() => {
    const userAgent = navigator.userAgent;
    isIOS.value = /iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream;
});

// 处理选择框点击事件
function handleSelectClick() {
    if (!isIOS.value) return;

    // 清除之前可能存在的定时器
    if (focusTimerId.value !== null) {
        clearTimeout(focusTimerId.value);
        focusTimerId.value = null;
    }

    // 在iOS设备上，先聚焦辅助输入框以唤起键盘
    if (assistantInputRef.value) {
        assistantInputRef.value.focus();

        // 延迟一段时间后将焦点转移到select的输入框
        focusTimerId.value = window.setTimeout(() => {
            const inputEl = selectRef.value?.$el?.querySelector('.el-input__inner');
            if (inputEl) {
                (inputEl as HTMLInputElement).focus();
            }
            focusTimerId.value = null;
        }, 1000);
    }
}

// 组件卸载前清除定时器
onBeforeUnmount(() => {
    if (focusTimerId.value !== null) {
        clearTimeout(focusTimerId.value);
        focusTimerId.value = null;
    }
});

function setBlur() {
    // 在iOS上，点击选择框时会触发输入框的focus事件
    // 但如果不手动设置blur，可能会导致输入法无法正常弹出
    if (assistantInputRef.value) {
        assistantInputRef.value.blur();
    }
    if (selectRef.value) {
        selectRef.value.blur();
    }
}
// 导出setBlur()方法，以便在需要时调用
defineExpose({
    setBlur,
});
</script>
<template>
    <div class="oa-organ-select-container">
        <!-- 辅助输入框，用于在iOS上触发软键盘 -->
        <input
            ref="assistantInputRef"
            class="ios-assistant-input"
            type="text"
            autocomplete="off"
        />

        <el-select
            ref="selectRef"
            v-model="organValue"
            filterable
            collapse-tags
            :placeholder="placeholder"
            :reserve-keyword="multiple"
            :loading="loading"
            :remote="true"
            class="oa-organ-select"
            clearable
            popper-class="oa-organ-select-popper"
            :remote-method="_debounceSearch"
            :multiple="multiple"
            @change="handleClinicIdsChange"
            @click="handleSelectClick"
        >
            <el-option
                v-for="item in organList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
                <!-- 使用自定义选项插槽，如果没有提供则使用默认内容 -->
                <slot name="option" :item="item" :display-organ="displayOrgan">
                    <div class="title">
                        {{ displayOrgan(item)?.name }}
                        <el-tag class="clinic-type" effect="dark" type="primary">{{ displayOrgan(item)?.clinicTypeName }}</el-tag>
                        <el-tag class="his-type" effect="dark" type="success">{{ displayOrgan(item)?.hisTypeName }}</el-tag>
                    </div>

                    <div v-if="showChain && displayOrgan(item)?.chainName" class="chain-name">连锁：{{ displayOrgan(item)?.chainName }}</div>
                    <div v-if="showAddress" class="address">{{ displayOrgan(item)?.address }}</div>
                    <div v-if="showAdministrator" class="administrator">
                        管理员：{{ displayOrgan(item)?.adminName }}
                        {{ displayOrgan(item)?.adminMobile }}
                    </div>
                </slot>
            </el-option>
        </el-select>
    </div>
</template>
<style lang="scss">
.oa-organ-select-container {
    position: relative;
    width: 100%;

    .ios-assistant-input {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        height: 1px;
        width: 1px;
        z-index: -1;
    }
}

.oa-organ-select {
    width: 100%;

    .el-select__tags {
        white-space: nowrap;
        flex-wrap: nowrap;
    }

    .el-input {
        .el-input__inner {
            height: 30px !important;
        }
    }
}

.oa-organ-select-popper {
    .el-select-dropdown__item {
        height: auto;
        line-height: auto;

        & > div {
            line-height: 20px;
        }

        .title {
            display: flex;
            align-items: center;
            font-weight: bold;
            font-size: var(--oa-font-size-14);
            color: #000;

            .clinic-type {
                margin-left: 4px;
            }

            .his-type {
                margin-left: 4px;
                background: var(--oa-success-color);
            }
        }

        .chain-name {
            font-weight: bold;
        }
    }

    .el-select-dropdown__item + .el-select-dropdown__item {
        margin-top: 8px;
    }
}
</style>
