<script setup lang="ts" >
import { computed, ref } from 'vue';
import { ElMessage } from 'element-plus';
import _ from 'lodash';
import { CommonAPI } from '@abc-oa/common';

const props = defineProps({
    value: {
        type: [String, Array],
        default: '',
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: '请选择用户',
    },
});
const emit = defineEmits(['update:value', 'change']);
const modelValue = computed({
    get() {
        return props.value;
    },
    set(val) {
        emit('update:value', val);
    },
});
const loading = ref(false);

const employeeOptions = ref([]);
async function fetchSelectOptions(keyword: string) {
    let res: any = {};
    loading.value = true;
    console.log(CommonAPI);
    try {
        res = await CommonAPI.CrmClientApi.getApiLowCodeCrmEmployeeList(keyword);
    } catch (err: any) {
        ElMessage.error(err.message || err);
        return;
    } finally {
        loading.value = false;
    }
    employeeOptions.value = res.rows?.map((row: any) => ({
        ...row,
        label: row.name,
        value: row.id,
    })) || [];
}
const debounceFetchSelectOptions = _.debounce(fetchSelectOptions, 300);

function handleEmployeeIdsChange(values: string | string[]) {
    emit('change', values);
}
</script>
<template>
    <el-select
        v-model="modelValue"
        filterable
        collapse-tags
        :placeholder="placeholder"
        :reserve-keyword="multiple"
        :loading="loading"
        :remote="true"
        class="oa-employee-select"
        clearable
        :remote-method="debounceFetchSelectOptions"
        :multiple="multiple"
        @change="handleEmployeeIdsChange"
    >
        <el-option
            v-for="item in employeeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        >
            <el-space>
                <div class="title">{{ item.name }}</div>
                <div class="mobile">{{ item.mobile }}</div>
            </el-space>
        </el-option>
    </el-select>
</template>
<style lang="scss">
.oa-employee-select {
    .el-select__tags {
        white-space: nowrap;
        flex-wrap: nowrap;
    }
}
</style>
