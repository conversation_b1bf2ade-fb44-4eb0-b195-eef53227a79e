import { HisType, editionAllOptions } from './clinic';

function formatAddress(address: any) {
    if (!address) return '';
    const {
        addressCityName,
        addressDetail,
        addressDistrictName,
        addressProvinceName,
    } = address || {};
    let str = '';
    str += addressProvinceName || '';
    str += addressCityName || '';
    str += addressDistrictName || '';
    str += addressDetail || '';
    return str;
}

/**
 * 连接地址
 * @param address
 * @param separator
 */
function concatAddress(address: any, separator = '/') {
    if (!address) return '';
    const {
        addressCityName,
        addressDetail,
        addressDistrictName,
        addressProvinceName,
    } = address || {};
    return [
        addressProvinceName,
        addressCityName,
        addressDistrictName,
        addressDetail,
    ].filter(item => !!item).join(separator);
}

/**
 * 格式化金额
 * @param number
 */
function formatMoney(number: number, prepend?: string) {
    number = number || 0;
    let moneyStr = (Math.round(number * 100) / 100)
                    .toFixed(2)
                    .toString()
                    .replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ''));
    if (prepend) {
        if (moneyStr.startsWith('-')) {
            moneyStr = `-${prepend}${moneyStr.slice(1)}`;
        } else {
            moneyStr = `${prepend}${moneyStr}`;
        }
    }
    return moneyStr;
}

/**
 * 格式化 HIS 类型
 * @param hisType
 */
function formatHisTypeName(hisType: HisType, isABC = true) {
    const options = {
        [HisType.DENTISTRY]: `${isABC ? 'ABC' : ''}口腔管家`,
        [HisType.NORMAL]: `${isABC ? 'ABC' : ''}诊所管家`,
        [HisType.OPHTHALMOLOGY]: `${isABC ? 'ABC' : ''}眼视光管家`,
        [HisType.HOSPITAL]: `${isABC ? 'ABC' : ''}智慧医院`,
        [HisType.PHARMACY]: `${isABC ? 'ABC' : ''}药店管家`,
    };
    return options[hisType] || '';
}

/**
 * formatEdition
 * @param edition 版本
 */
function formatEdition(edition: string) {
    return editionAllOptions.find(e => +e.value === +edition)?.label;
}

function newDate(date?: any): Date {
    // 判定时间为ISO格式
    if (date && typeof date === 'string' && date.match(/T/)) {
        const isoDate = new Date(date);
        if (!Number.isNaN(isoDate.getTime())) {
            return isoDate;
        }
        const isoDateArr = date.split(/[-T:+]/).map((item) => parseInt(item, 10));
        if (isoDateArr.length === 7) {
            return new Date(
                isoDateArr[0],
                isoDateArr[1] - 1,
                isoDateArr[2],
                isoDateArr[3],
                isoDateArr[4],
                isoDateArr[5],
            );
        }
    }
    if (date && typeof date === 'string') {
        // 将横杠替换为斜杠
        date = date.replace(/-/g, '/');
    }
    if (!date) {
        return new Date();
    }
    return new Date(date);
}
/**
 * @description: 格式化日期
 * @date: 2024-07-18 11:35:00
 * @author: Horace
 * @param {Date | string | number} date 时间
 * @param {string} fmt 格式
 * @return
 */
function formatDate(date: Date | string | number, fmt = 'YYYY-MM-DD HH:mm:ss'): string {
    if (!date) {
        return '';
    }

    date = newDate(date);
    const obj: any = {
        'Y+': date.getFullYear(),
        'M+': date.getMonth() + 1,
        'D+': date.getDate(),
        'H+': date.getHours(),
        'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'S+': date.getMilliseconds(),
    };

    const week = ['天', '一', '二', '三', '四', '五', '六'];

    if (/(Y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }

    if (/(w+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length > 1 ? '星期' : '周') + week[date.getDay()]);
    }

    for (const k in obj) {
        if (new RegExp(`(${k})`).test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? obj[k] + '' : ('00' + obj[k]).substr(('' + obj[k]).length),
            );
        }
    }

    return fmt;
}

/**
 * 获取指定天数前的日期（格式：YYYY-MM-DD）
 * @param days 天数
 * @returns 日期字符串
 */
function getDaysAgo(days: number): string {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return formatDate(date);
}

/**
 * 格式化时间
 * @param date
 * @param option { boolean } 为true 不显示凌晨等
 */
function formatTime(date: any, option = false) {
    let d = new Date(date);
    let today = new Date();
    today.setHours(0);
    today.setMinutes(0);
    today.setMinutes(0);
    // @ts-ignore
    let diff = (today * 1 - d * 1) / 1000;
    // @ts-ignore
    if (d * 1 - today * 1 >= 0) {
        let hour = d.getHours();
        let min = +d.getMinutes() > 9 ? d.getMinutes() : '0' + d.getMinutes();

        if (option) {
            // @ts-ignore
            hour = +hour > 9 ? hour : '0' + hour;
            return `${hour}${min}`;
        }
        if (hour >= 0 && hour < 6) {
            return `凌晨 ${hour}:${min}`;
        } if (hour >= 6 && hour < 12) {
            return `上午 ${hour}:${min}`;
        } if (hour >= 12 && hour < 13) {
            return `中午 12:${min}`;
        } if (hour >= 13 && hour < 18) {
            return `下午 ${hour - 12}:${min}`;
        }
        return `晚上 ${hour - 12}:${min}`;
    } if (diff > 0 && diff < 86400) {
        return '昨天';
    } if (diff > 86400 && diff <= 86400 * 2) {
        return '前天';
    }
    let month = (+d.getMonth() + 1) > 9 ? +d.getMonth() + 1 : '0' + (d.getMonth() + 1);
    let day = +d.getDate() > 9 ? d.getDate() : '0' + d.getDate();
    return month + '-' + day;
}

export {
    newDate,
    formatAddress,
    concatAddress,
    formatHisTypeName,
    formatEdition,
    formatMoney,
    formatTime,
    formatDate,
    getDaysAgo,
};
