/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 11:25:50 
 */
import SocialApi from '../api/social-api';
import AbcResponse from '../common/AbcResponse';
import { reactive, ref } from 'vue';

import explainInfoInterface from '../type/explainInfo';
import classifyInfoInterface from '../type/classifyInfo';

const createClassifyStore = () => {
    const isExplainInited = ref(false);
    const explainDataList = reactive<explainInfoInterface[]>([]);
    const isClassifyInited = ref(false);
    const classifyDataList = reactive<classifyInfoInterface[]>([]);

    /**
     * 初始化文案库列表
     * <AUTHOR>
     * @date 2023-11-14
     * @returns {AbcResponse}
     */
    const initExplainDataList = async () => {
        if (isExplainInited.value) {
            return AbcResponse.success();
        }
        const fetchResponse: AbcResponse = await SocialApi.fetchExplainList();
        if (fetchResponse.status === false) {
            return fetchResponse;
        }
        isExplainInited.value = true;
        explainDataList.value = fetchResponse.data?.rows || [];
        explainDataList.value.reverse();
        return fetchResponse;
    };
    /**
     * 初始化分类库列表
     * <AUTHOR>
     * @date 2023-11-14
     * @returns {AbcResponse}
     */
    const initClassifyDataList = async () => {
        if (isClassifyInited.value) {
            return AbcResponse.success();
        }
        const fetchResponse: AbcResponse = await SocialApi.fetchCategoryList();
        if (fetchResponse.status === false) {
            return fetchResponse;
        }
        isClassifyInited.value = true;
        classifyDataList.value = fetchResponse.data?.rows || [];
        classifyDataList.value.reverse();
        return fetchResponse;
    };
    /**
     * 添加文案库数据
     * <AUTHOR>
     * @date 2023-11-14
     * @param {explainInfoInterface} explainItem
     */
    const addExplainItem = (explainItem: explainInfoInterface) => {
        explainDataList.value.unshift(explainItem);
    };
    /**
     * 删除文案库数据
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} id
     */
    const delExplainItem = (id: string) => {
        explainDataList.value = explainDataList.value.filter(item => item.id !== id);
    };
    /**
     * 更新文案库数据
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} id
     * @param {explainInfoInterface} explainItem
     */
    const updateExplainItem = (id: string, explainItem: explainInfoInterface) => {
        const index = explainDataList.value.findIndex(item => item.id === id);
        explainDataList.value.splice(index, 1, explainItem);
    };
    /**
     * 添加归类库数据
     * <AUTHOR>
     * @date 2023-11-14
     * @param {classifyInfoInterface} classifyItem
     */
    const addClassifyItem = (classifyItem: classifyInfoInterface) => {
        classifyDataList.value.unshift(classifyItem);
    };
    /**
     * 删除归类库数据
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} id
     */
    const delClassifyItem = (id: string) => {
        classifyDataList.value = classifyDataList.value.filter(item => item.id !== id);
    };
    /**
     * 更新归类库数据
     * <AUTHOR>
     * @date 2023-11-14
     * @param {String} id
     * @param {classifyInfoInterface} classifyItem
     */
    const updateClassifyItem = (id: string, classifyItem: classifyInfoInterface) => {
        const index = classifyDataList.value.findIndex(item => item.id === id);
        classifyDataList.value.splice(index, 1, classifyItem);
    };

    return {
        isExplainInited,
        explainDataList,
        isClassifyInited,
        classifyDataList,
        initClassifyDataList,
        initExplainDataList,
        addExplainItem,
        delExplainItem,
        updateExplainItem,
        addClassifyItem,
        delClassifyItem,
        updateClassifyItem,
    };
};

export default createClassifyStore();