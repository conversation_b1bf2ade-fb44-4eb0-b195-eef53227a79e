/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import * as utils from '../common/utils';
import { reactive, computed } from 'vue';

import createLoadingModel from '../model/loading';
import SocialApi from '../api/social-api';

export const createPageFeatureController = () => {
    const loadingModelData = createLoadingModel();

    // 地区列表
    const regionList = reactive({
        list: <any>[],
    });

    // 配置列表
    const configList = reactive({
        list: <any>[],
    });

    // 工具栏参数
    const toolsParams = reactive({
        provinceNames: <any>[],
        searchKeyword: '',
    });

    // 地区筛选
    const provinceOptions = computed(() => {
        const options = <any>[];
        regionList.list.forEach((item: any) => {
            let label = item.provinceName === '直辖市' ? item.regionName : item.provinceName;
            if (!options.includes(label)) {
                options.push(label);
            }
        });
        return options;
    });

    // 展示表单项
    const showTableCol = computed(() => {
        const tableCol = <any>[];
        const confList = utils.cloneDeep(configList.list);
        confList.forEach((options: any) => {
            handleFilter(options).forEach((item: any) => pushColItem(tableCol, item));
        });
        return tableCol;
    });

    // 展示数据
    const showDataList = computed(() => {
        const dataList = <any>[];
        const confList = utils.cloneDeep(configList.list);
        confList.forEach((options: any) => {
            const item = createDataItem({}, options);
            dataList.push(item);
        });
        return dataList;
    });

    /**
     * 处理过滤
     * <AUTHOR>
     * @date 2025-01-09
     * @param {Array} list:any
     * @returns {Array}
     */
    const handleFilter = (list: any) => {
        if (!toolsParams.searchKeyword) {
            return list;
        }
        const propList = [
            'regionName',
            'regionPrincipal',
        ];
        const check = (label: string) => label && label.indexOf(toolsParams.searchKeyword) !== -1;
        
        return list.filter((item: any) => {
            if (propList.includes(item.prop)) {
                return true;
            }
            if (check(item.label)) {
                return true;
            }
            if (item.children) {
                item.children = handleFilter(item.children || []);
                return !!item.children.length;
            }
            if (item.prop) {
                if (propList.includes(item.prop)) {
                    return true;
                }
                return check(item.head.label);
            }
            return false;
        });
    };

    /**
     * push一个col结构
     * <AUTHOR>
     * @date 2024-03-22
     * @param {Array} children
     * @param {Object} item
     * @returns {Array}
     */
    const pushColItem = (children: any, item: any) => {
        if (item.id && item.children) {
            // 有子集，设置children结构
            let boxItem = children.find((one: any) => one.id === item.id);
            if (!boxItem) {
                boxItem = {
                    id: item.id,
                    label: item.label,
                    children: [],
                };
                children.push(boxItem);
            }
            item.children.forEach((two: any) => {
                pushColItem(boxItem.children, two);
            });
        } else {
            // 是prop项
            let colItem = children.find((one: any) => one.prop === item.prop);
            if (!colItem) {
                colItem = createColItem(item);
                children.push(colItem);
            }
        }
        return children;
    };
    /**
     * 创建一条数据对象
     * <AUTHOR>
     * @date 2024-03-22
     * @param {Object} dataItem
     * @param {Array} options
     * @returns {Object}
     */
    const createDataItem = (dataItem: any, options: any) => options.reduce((obj: any, item: any) => {
        if (item.id && item.children) {
            // 有子集，递归处理
            createDataItem(obj, item.children);
        } else {
            // 是prop项
            const dataValue = createDataValue(item);
            obj[item.prop] = dataValue;
        }
        return obj;
    }, dataItem);

    /**
     * 创建col项
     * <AUTHOR>
     * @date 2024-03-22
     * @param {Object} item
     * @returns {Object}
     */
    const createColItem = (item: any) => {
        const colItem = {
            prop: item.prop,
            ...({
                width: 200, // 默认200宽
                fixed: false, // 默认不固定
                showOverflowTooltip: true, // 默认超出tooltips展示
            }),
            ...(item.head || {}),
        };
        return colItem;
    };

    /**
     * 创建data的值
     * <AUTHOR>
     * @date 2024-03-22
     * @param {Object} item
     * @returns {Object}
     */
    const createDataValue = (item: any) => {
        const dataValue = {
            prop: item.prop,
            ...({
                isPopover: false,
            }),
            ...(item.body || {}),
        };
        return dataValue;
    };

    // 初始化
    const init = async () => {
        // 加载social资源
        const loadResponse = await utils.loadSocial();
        if (loadResponse.status === false) {
            return loadResponse;
        }
        // 设置地区列表
        regionList.list = await createRegionList();
        toolsParams.provinceNames.push(provinceOptions.value[0]);
    };

    /**
     * 创建地区列表
     * <AUTHOR>
     * @date 2024-03-20
     * @returns {Promise<Array>}
     */
    const createRegionList = async () => {
        const regionList = [];
        const regionExportList = window.getRegionExportList();
        for (let index = 0; index < regionExportList.length; index++) {
            const regionExportItem = regionExportList[index];
            if (!regionExportItem) {
                continue;
            }
            const config = await regionExportItem.options.config().then((module: any) => {
                const Class = module.default;
                return new Class();
            });
            regionList.push({
                region: regionExportItem.regions[0],
                setlOptins: config.setlOptins,
                provinceName: config.provinceName,
                regionName: config.regionName,
            });
        }
        return regionList.sort((a: any, b: any) => a.setlOptins - b.setlOptins);
    };

    /**
     * 刷新配置列表
     * <AUTHOR>
     * @date 2024-03-20
     * @returns {Promise<AbcResponse>}
     */
    const refreshConfigList = async () => {
        const list = <any>[];
        const userCountResponse = await SocialApi.fetchRegionUserCount();
        for (let index = 0; index < regionList.list.length; index++) {
            const item = regionList.list[index];
            const isMate = (
                toolsParams.provinceNames.includes(item.provinceName)
                || toolsParams.provinceNames.includes(item.regionName)
            );
            if (!isMate) {
                // 没匹配上
                continue;
            }
            const mode = 'social'; // 完整模式
            const nationalInstall = window.createNationalInstall(mode, item.region);
            await nationalInstall.init();
            window.$platform.context.store = nationalInstall.createStore();
            const featureOptions = nationalInstall.$national.options.featureOptions;
            const newFeatureOptions = insertUserCountFeatureOption(featureOptions, userCountResponse, item);
            list.push(newFeatureOptions);
        }
        configList.list = list;
    };
    
    /**
     * 功能展示增加用户数据
     * <AUTHOR>
     * @date 2025-05-23
     * @return {Array}
     */
    const insertUserCountFeatureOption = (featureOptions: any[], userCountResponse: any, item: { region: string }) => {
        const newFeatureOptions = [...featureOptions];
        const { 
            clinicCountList = [],
        } = (userCountResponse.data || []).find((count: { region: string; }) => count.region === item.region) || {};
        newFeatureOptions.splice(1, 0, {
            id: 'userCount',
            label: '活跃用户/总开通',
            children: [
                {
                    prop: 'clinic',
                    head: {
                        label: '诊所',
                        width: 140,
                    },
                    body: {
                        content: getClinicCounts(clinicCountList, 2),
                    },
                },
                {
                    prop: 'pharmacy',
                    head: {
                        label: '药店',
                        width: 140,
                    },
                    body: {
                        content: getClinicCounts(clinicCountList, 3),
                    },
                },
                {
                    prop: 'hospital',
                    head: {
                        label: '医院',
                        width: 140,
                    },
                    body: {
                        content: getClinicCounts(clinicCountList, 1),
                    },
                },
            ],
        });
        return newFeatureOptions;
    };
    /**
     * 获取活跃计数信息
     * <AUTHOR>
     * @date 2025-08-07
     * @param clinicCountList - 诊所计数列表
     * @param clinicType - 机构类型 1(医院) 2(诊所) 3(药店)
     * @returns {string} - 活跃/总开通
     */
    const getClinicCounts = (clinicCountList: any[], clinicType: number) => {
        const clinicCountItem = clinicCountList.find((count: any) => count.clinicType === clinicType) || {};
        const { recentCount = 0, totalCount = 0 } = clinicCountItem;
        return `${recentCount}/${totalCount}`;
    };
    return {
        loadingModelData,
        toolsParams,
        provinceOptions,
        showTableCol,
        showDataList,
        init,
        refreshConfigList,
    };
};