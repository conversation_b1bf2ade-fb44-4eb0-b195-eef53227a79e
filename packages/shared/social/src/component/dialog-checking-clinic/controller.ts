/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import SocialApi from '../../api/social-api';
import AbcResponse from '../../common/AbcResponse';
import classifyStore from '../../store/classifyStore';
import * as utils from '../../common/utils';
import { reactive, computed } from 'vue';

import createLoadingModel from '../../model/loading';
import createDialogModel from '../../model/dialog';
import createPageModel from '../../model/page';

export const createCheckingClinicController = (props: any) => {
    const loadingModel = createLoadingModel();
    const dialogModel = createDialogModel();
    const pageModel = createPageModel();

    const toolsParams = reactive({
        keyword: '', // 关键词
        status: props.status, // 状态
    });

    // 状态选项
    const statusOptions = Object.freeze([
        { value: '', label: '全部' },
        { value: 0, label: '未对账' },
        { value: 1, label: '对账成功' },
        { value: 2, label: '对账失败' },
    ])

    // 匹配的数据列表
    let cacheToolsParams = {};
    const mateDataList = computed(() => {
        let dataList = props.clinicStatusList
        if (props.region) {
            dataList = dataList.filter((item: any) => item.region === props.region);
        }
        if (toolsParams.keyword) {
            // 通过关键字过滤
            dataList = dataList.filter((item: any) => (
                item.clinicId.indexOf(toolsParams.keyword) !== -1
                || item.hospitalCode.indexOf(toolsParams.keyword) !== -1
                || item.hospitalName.indexOf(toolsParams.keyword) !== -1
            ));
        }
        if (toolsParams.status !== '') {
            // 通过是否配文过滤
            dataList = dataList.filter((item: any) => {
                switch (toolsParams.status) {
                    case 0:
                        // 未对账
                        return item.checkingStatus === 0;
                    case 1:
                        // 对账成功
                        return item.checkingStatus === 1;
                    case 2:
                        // 对账失败
                        return item.checkingStatus === 2;
                    default:
                        break;
                }
                return false;
            });
        }
        if (!utils.isEqual(cacheToolsParams, toolsParams)) {
            pageModel.setTotal(dataList.length);
            pageModel.setPage(1);
            cacheToolsParams = utils.cloneDeep(toolsParams);
        }
        return dataList;
    });

    // 显示的数据列表
    const showDataList = computed(() => {
        const { sIndex, eIndex } = pageModel.createSliceParams();
        return mateDataList.value.slice(sIndex, eIndex).map((item: any) => {
            const itemInfo = {
                ...item,
                checkingStatusType: '',
                checkingStatusText: '',
            };
            switch (item.checkingStatus) {
                case 0:
                    // 未对账
                    itemInfo.checkingStatusType = 'primary';
                    itemInfo.checkingStatusText = '未对账';
                    break;
                case 1:
                    // 已对账
                    itemInfo.checkingStatusType = 'success';
                    itemInfo.checkingStatusText = '对账成功';
                    break;
                case 2:
                    // 对账失败
                    itemInfo.checkingStatusType = 'warning';
                    itemInfo.checkingStatusText = '对账失败';
                    break;
                default:
                    break;
            }
            return itemInfo;
        });
    });

    /**
     * 请求登录门店Href
     * <AUTHOR>
     * @date 2025-01-03
     * @param {String} clinicId
     * @returns {Promise<AbcResponse>}
     */
    const requestLoginClinicHref = async (clinicId: string) => {
        const params = {
            clinicId, // 门店ID
            clinicName: '', // 门店名称
            isAdmin: 1, // 管理员
            offset: 0,
            limit: 1,
        };
        const response = await SocialApi.fetchCrmClinicInfo(params);
        if (response.status === false) {
            return response;
        }
        const item = (response.data?.rows || [])[0];
        if (!item) {
            return AbcResponse.error('未找到当前门店成员');
        }
        return AbcResponse.success({
            openId: item.employeeOpenId || item.employeeMobile,
            clinicId,
        });
    };

    return {
        classifyStore,
        loadingModel,
        dialogModel,
        pageModel,
        toolsParams,
        statusOptions,
        showDataList,
        requestLoginClinicHref,
    };
};