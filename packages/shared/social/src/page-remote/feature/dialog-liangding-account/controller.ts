/*
 * <AUTHOR> 
 * @DateTime 2025-01-13 20:09:15 
 */
import AbcResponse from '../../../common/AbcResponse';
import { reactive } from 'vue';

/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
export const createLiangdingAccountController = (props: any) => {
    const queryResponse = reactive({
        accountInfo: <any> null,
    });

    /**
     * 请求账号信息
     * <AUTHOR>
     * @date 2024-08-07
     * @returns {AbcResponse}
     */
    const requestAccountInfo = () => {
        const liangding = props.socialInfo?.basicInfo?.liangding || null;
        if (!liangding) {
            return AbcResponse.error('门店未绑定两定账号');
        }
        const accountInfo = window.$national.tools.decodeAccountInfo(liangding);
        return AbcResponse.success(accountInfo);
    };

    return {
        queryResponse,
        requestAccountInfo,
    };
};