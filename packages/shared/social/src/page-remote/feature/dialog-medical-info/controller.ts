/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';

export const createSettleInfoController = (props: any) => {
    const loadingModelQuery = createLoadingModel();
    const loadingModelMore = createLoadingModel();

    const formData = reactive({
        dateRange: [], // 日期范围
        medType: '', // 医疗类别
        insuplcAdmdvs: '', // 参保区划
        psnNo: '', // 人员编号
    });

    const queryResponse = reactive({
        medicalInfoList: <any []>[], // 就诊记录
        medicalInfo: <any> null, // 就诊信息 - 当前选中的
        diseinfoList: <any []>[], // 诊断信息
        chargeDetailList: <any []>[], // 费用明细
        settleInfoList: <any []>[], // 结算信息
    });

    const medicalInfoList = computed(() => queryResponse.medicalInfoList.map((item: any) => {
        const itemInfo = {
            ...item,
            isSelected: item.mdtrt_id === queryResponse.medicalInfo?.mdtrt_id, // 是否选中
        };
        return itemInfo;
    }));

    // 是否禁用查询按钮
    const isDisabledQueryBtn = computed(() => !(
        formData.psnNo
            && formData.dateRange
            && formData.medType
    ));

    // 医疗类别选项
    const medicalCategoryOptions = computed(() => window.$national.options.medicalCategoryOptions);

    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2024-08-14
     */
    const initFormData = () => {
        const date = window.$national.tools.getDateFormat();
        Object.assign(formData, {
            dateRange: [date, date], // 日期范围
            medType: medicalCategoryOptions.value[0].value, // 医疗类别
            psnNo: '', // 人员编号
            insuplcAdmdvs: '', // 参保区划
        });
    };

    const resetMoreInfo = () => {
        queryResponse.diseinfoList = []; // 诊断信息
        queryResponse.chargeDetailList = []; // 费用明细
        queryResponse.settleInfoList = []; // 结算信息
    };

    /**
     * 创建就诊信息查询参数
     * <AUTHOR>
     * @date 2024-08-06
     * @returns {Object}
     */
    const createMedicalInfoParams = () => {
        const {
            dateRange, // 日期范围
            medType, // 医疗类别
            insuplcAdmdvs, // 参保区划
            psnNo, // 人员编号
        } = formData;
        const params = {
            insuplcAdmdvs, // 参保区划
            data: {
                begntime: `${window.$national.tools.getDateFormat(dateRange[0])} 00:00:00`, // 开始时间
                endtime: `${window.$national.tools.getDateFormat(dateRange[1])} 23:59:59`, // 结束时间
                med_type: medType, // 医疗类别
                psn_no: psnNo, // 人员编号
            },
        };
        return params;
    };

    /**
     * 请求就诊记录
     * <AUTHOR>
     * @date 2024-10-12
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestMedicalInfoList = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            const insuplcAdmdvs = '${params.insuplcAdmdvs}'
            const medicalInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.medicalInfo(params, insuplcAdmdvs)
            if (medicalInfoResponse.status === false) {
                throw medicalInfoResponse
            }
            const medicalInfoList = medicalInfoResponse.data.output?.mdtrtinfo || []
            response = { status: true, data: { medicalInfoList } }
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    /**
     * 创建更多信息参数
     * <AUTHOR>
     * @date 2024-08-06
     * @returns {Object}
     */
    const createMoreInfoParams = () => {
        const {
            psn_no: psnNo, // 人员编号
            mdtrt_id: mdtrtId, // 就诊ID
        } = queryResponse.medicalInfo;
        const {
            insuplcAdmdvs, // 参保区划
        } = formData;
        const params = {
            psnNo, // 人员编号
            mdtrtId, // 就诊ID
            insuplcAdmdvs, // 参保区划
        };
        return params;
    };

    /**
     * 请求更多信息
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestMoreInfo = async (params: any) => {
        const js = `
            const {
                psnNo,
                mdtrtId,
                insuplcAdmdvs,
            } = JSON.parse('${JSON.stringify(params)}')
            
            // 诊断信息查询
            const diagnosticInfoParams = {
                data: {
                    psn_no: psnNo,
                    mdtrt_id: mdtrtId,
                },
            }
            const diagnosticInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.diagnosticInfo(diagnosticInfoParams, insuplcAdmdvs)
            const diseinfoList = diagnosticInfoResponse.data?.output?.diseinfo || null

            // 费用明细查询
            const chargeDetailParams = {
                data: {
                    psn_no: psnNo,
                    mdtrt_id: mdtrtId,
                },
            }
            const chargeDetailResponse = await window.$national.protocol.NationalSocialSecurity.instance.chargeDetail(chargeDetailParams, insuplcAdmdvs)
            const chargeDetailList = chargeDetailResponse.data?.output || []
            
            // 结算信息查询
            const setlIdList = []
            const settleInfoList = []
            chargeDetailList.forEach((item) => {
                if (item.setl_id && !setlIdList.includes(item.setl_id)) {
                    setlIdList.push(item.setl_id)
                }
            })
            for (let index = 0; index < setlIdList.length; index++) {
                const settleInfoParams = {
                    data: {
                        psn_no: psnNo,
                        mdtrt_id: mdtrtId,
                        setl_id: setlIdList[index],
                    },
                }
                const settleInfoResponse = await window.$national.protocol.NationalSocialSecurity.instance.settleInfo(settleInfoParams, insuplcAdmdvs)
                const setlinfo = settleInfoResponse.data?.output || null
                settleInfoList.push(setlinfo)
            }
            response = {
                status: true,
                data: {
                    diseinfoList,
                    chargeDetailList,
                    settleInfoList,
                }
            }
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelQuery,
        loadingModelMore,
        formData,
        queryResponse,
        medicalInfoList,
        isDisabledQueryBtn,
        medicalCategoryOptions,
        initFormData,
        resetMoreInfo,
        createMedicalInfoParams,
        requestMedicalInfoList,
        createMoreInfoParams,
        requestMoreInfo,
    };
};