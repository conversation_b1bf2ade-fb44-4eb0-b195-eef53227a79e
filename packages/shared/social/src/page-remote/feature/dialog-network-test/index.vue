<template>
    <el-dialog
        v-model="isShowDialogNetworkTest"
        title="网络调试"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-network-test"
    >
        <div class="cont-wrapper">
            <div class="form-item-wrapper">
                <div class="label">常用命令</div>
                <el-cascader
                    v-model="formData.actions"
                    :options="actionOptions"
                    popper-class="client-service__technical-support__dialog-network-test__action-popper"
                    @change="onChangeAction"
                ></el-cascader>
            </div>
            <div class="form-item-wrapper">
                <div class="label">命令编辑</div>
                <el-input
                    v-model="formData.command"
                    type="textarea"
                    :autosize="{ minRows: 1, maxRows: 10 }"
                    :disabled="isDisabledCommand"
                    :placeholder="isDisabledCommand ? '无需编辑命令，可直接执行' : '选择常用命令或手动输入命令执行'"
                ></el-input>
                <el-button
                    type="primary"
                    :disabled="isDisabledExecuteBtn"
                    :loading="loadingModel.loading.value"
                    @click="onClickExec"
                >
                    执行命令
                </el-button>
            </div>
            <div class="line"></div>
            <div class="result-wrapper">
                <div v-if="loadingModel.loading.value" class="loading-box">
                    <van-loading size="24px">正在执行命令</van-loading>
                </div>
                <el-input
                    v-else-if="remoteInfo.execMessage"
                    :value="remoteInfo.execMessage"
                    type="textarea"
                    disabled
                ></el-input>
                <el-empty
                    v-else
                    :image-size="80"
                    description="无命令执行结果"
                ></el-empty>
            </div>
        </div>
        <div class="foot-wrapper">
            <el-button
                type="primary"
                size="small"
                :disabled="isDisabledCopyResultBtn"
                @click="onClickCopyResult"
            >
                复制命令结果
            </el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { computed, onMounted } from 'vue';
import { createDialogNetworkTestController } from './controller';
import * as utils from '../../../common/utils';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    deviceCode: {
        type: String,
        default: '',
    },
});

const $emit = defineEmits([
    'update:visible', // 更新显示状态
    'cancel', // 取消
]);

const isShowDialogNetworkTest = computed({
    get() {
        return !!props.visible;
    },
    set(val: boolean) {
        $emit('update:visible', val);
        if (val === false) {
            $emit('cancel');
        }
    },
});

const {
    loadingModel,
    formData,
    remoteInfo,
    isDisabledExecuteBtn,
    isDisabledCopyResultBtn,
    actionOptions,
    actionConfigItem,
    isDisabledCommand,
    initFormData,
    requestTelnet,
    requestExecSync,
    insertSoicalNetworkConfig,
} = createDialogNetworkTestController();

onMounted(() => {
    remoteInfo.deviceCode = props.deviceCode;
    insertSoicalNetworkConfig();
    initFormData();
});

/**
 * 当改变动作时
 * <AUTHOR>
 * @date 2024-09-05
 */
const onChangeAction = async () => {
    if (!actionConfigItem) {
        return;
    }
    formData.command = actionConfigItem.value?.command || '';
    remoteInfo.execMessage = '';
    loadingModel.setLoading(false);
};

/**
 * 当点击执行
 * <AUTHOR>
 * @date 2024-09-05
 */
const onClickExec = async () => {
    if (!actionConfigItem) {
        return;
    }
    const tips = actionConfigItem.value?.tips || '';
    if (tips) {
        const confirmResponse = await utils.messageConfirm(tips, '注意', {
            type: 'warning',
            confirmButtonText: '继续',
            cancelButtonText: '取消',
        });
        if (confirmResponse.status === false) {
            return;
        }
    }
    loadingModel.setLoading(true);
    let execPromise: any = null;
    let timeout: number = 1000 * 20; // 默认都是20s超时时间
    if (actionConfigItem.value?.exec) {
        execPromise = actionConfigItem.value?.exec();
    } else {
        const command = formData.command.trim();
        execPromise = (() => {
            if (command.startsWith('ping')) {
                // ping命令
                timeout = 1000 * 3600 * 1; // ping命令执行时间很久，默认1分钟超时时间
                window.remoteSDK.rpcInvoker.invokeTimeoutOnce = timeout;
                return requestExecSync(command);
            }
            if (command.startsWith('tracert')) {
                // tracert命令
                timeout = 1000 * 3600 * 5; // tracert命令执行时间很久，默认5分钟超时时间
                window.remoteSDK.rpcInvoker.invokeTimeoutOnce = timeout;
                return requestExecSync(command);
            }
            if (command.startsWith('telnet')) {
                // telnet命令
                return requestTelnet(command);
            }
            // 其他命令
            return requestExecSync(command);
        })();
    }
    const response = await utils.runTimeoutPack(execPromise, timeout);
    loadingModel.setLoading(false);
    if (response.status === false) {
        remoteInfo.execMessage = response.message;
        return;
    }
    remoteInfo.execMessage = response.data?.result || '执行成功';
};

/**
 * 当点击拷贝结果时
 * <AUTHOR>
 * @date 2024-09-04
 */
const onClickCopyResult = () => {
    utils.copy(remoteInfo.execMessage);
    ElMessage({
        type: 'success',
        message: '复制成功',
        offset: 320,
    });
};
</script>

<style lang="scss">
    .social-module__feature__dialog-network-test {
        width: 960px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .head-wrapper {
            font-weight: bold;
        }

        .cont-wrapper {
            height: 72vh;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: stretch;

            .form-item-wrapper {
                min-height: 32px;
                display: flex;
                align-items: flex-start;
                margin-bottom: 10px;

                .label {
                    line-height: 32px;
                    width: 80px;
                    flex-shrink: 0;
                    text-align: left;
                }

                .el-cascader {
                    width: 300px;
                }

                .el-input {
                    flex: 1;
                }

                .el-button {
                    flex-shrink: 0;
                    margin-left: 12px;
                }

                .el-textarea__inner {
                    max-height: 180px;
                }
            }

            .line {
                width: 100%;
                border-top: 1px solid #dcdfe6;
                margin-bottom: 10px;
            }

            .result-wrapper {
                flex: 1;

                .loading-box {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                .el-textarea {
                    height: calc(100% - 30px);
                    margin-top: 4px;

                    .el-textarea__inner {
                        color: #333;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 16px;
                        resize: none;
                        border: 0;
                        padding: 0;
                        box-shadow: none;
                        background-color: #fdfdfd;
                        height: 100%;
                        overflow-y: auto;
                    }
                }

                .el-empty {
                    padding-top: 180px;
                }
            }
        }

        .foot-wrapper {
            display: flex;
            justify-content: flex-end;
        }
    }

    .client-service__technical-support__dialog-network-test__action-popper {
        .el-cascader-menu__wrap {
            height: auto;
        }

        .el-cascader-panel {
            > .el-cascader-menu {
                min-width: 136px;
            }
        }
    }
</style>