/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import createLoadingModel from '../../../model/loading';

export const createDictionaryController = (props: any) => {
    const loadingModelQuery = createLoadingModel();

    const formData = reactive({
        type: '', // 字典类型
        parentValue: '', // 父字典键值
        admdvs: '', // 行政区划
        date: '', // 查询日期
        valiFlag: '', // 有效标志
    });

    const queryResponse = reactive({
        originData: <any> null,
    });

    // 是否禁用查询按钮
    const isDisabledQueryBtn = computed(() => !(
        formData.type
        && formData.date
        && formData.valiFlag
    ));

    // 字典类型选项
    const dictionaryTypeOptions = computed(() => window.$national.options.dictionaryTypeOptions);

    // 有效标识选项
    const valiFlagOptions = computed(() => window.$national.options.valiFlagOptions);

    // 展示数据
    const showDataList = computed(() => (queryResponse.originData?.output?.list || []).map((item: any) => {
        const itemInfo = {
            ...item,
        };
        itemInfo.typeWording = window.$national.tools.getDictionaryTypeWording(item.type);
        itemInfo.createDateWording = item.create_date ? window.$national.tools.getDatetimeFormat(item.create_date) : '';
        return itemInfo;
    }));

    /**
     * 初始化表单数据
     * <AUTHOR>
     * @date 2024-08-14
     */
    const initFormData = () => {
        Object.assign(formData, {
            type: '', // 字典类型
            parentValue: '', // 父字典键值
            admdvs: window.$national.config.setlOptins, // 行政区划
            date: window.$national.tools.getDateFormat(), // 查询日期
            valiFlag: valiFlagOptions.value[1].value, // 有效标志
        });
    };

    /**
     * 创建查询参数
     * <AUTHOR>
     * @date 2024-08-06
     * @returns {Object}
     */
    const createParams = () => {
        const {
            type, // 字典类型
            parentValue, // 父字典键值
            admdvs, // 行政区划
            date, // 查询日期
            valiFlag, // 有效标志
        } = formData;
        const params = {
            data: {
                type, // 字典类型
                parent_value: parentValue, // 父字典键值
                admdvs, // 行政区划
                date, // 查询日期
                vali_flag: valiFlag, // 有效标志
            },
        };
        return params;
    };

    /**
     * 请求字典查询
     * <AUTHOR>
     * @date 2024-08-07
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestDictionary = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            response = await window.$national.protocol.NationalSocialSecurity.instance.dictionaryTable(params)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelQuery,
        formData,
        queryResponse,
        isDisabledQueryBtn,
        dictionaryTypeOptions,
        valiFlagOptions,
        showDataList,
        initFormData,
        createParams,
        requestDictionary,
    };
};