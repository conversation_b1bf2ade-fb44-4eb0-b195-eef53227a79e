<template>
    <el-dialog
        v-model="isShowDialogErrorSettle"
        title="异常结算数据"
        :close-on-click-modal="false"
        custom-class="social-module__feature__dialog-error-settle"
    >
        <div class="tools-wrapper">
            <el-date-picker
                v-model="toolsParams.month"
                type="month"
                :clearable="false"
                style="width: 160px; margin-right: 8px;"
            ></el-date-picker>
            <el-button
                type="primary"
                :loading="loadingModelQuery.loading.value"
                @click="onClickQuery"
            >
                查询
            </el-button>
        </div>
        <el-table
            v-loading="loadingModelQuery.loading.value"
            :data="showDataList"
            :height="375"
            border
            style="width: 100%;"
        >
            <el-table-column prop="typeWording" label="类型" min-width="110"></el-table-column>
            <el-table-column
                prop="patientName"
                label="患者姓名"
                min-width="110"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="psnName"
                label="人员姓名"
                min-width="110"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column prop="medfeeSumamtWording" label="医疗费用总额" min-width="140"></el-table-column>
            <el-table-column prop="fundPaySumamtWording" label="基金支付金额" min-width="140"></el-table-column>
            <el-table-column prop="acctPayWording" label="个人账户支出" min-width="140"></el-table-column>
            <el-table-column prop="settleTimeWording" label="结算发起时间" min-width="180"></el-table-column>
            <el-table-column
                prop="insuplcAdmdvsWording"
                label="参保地"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="clrTypeWording"
                label="清算类别"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="insutypeWording"
                label="险种类型"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="clrOptinsWording"
                label="经办机构"
                min-width="160"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="iptOtpNo"
                label="门诊号/住院号"
                min-width="180"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="psnNo"
                label="人员编号（psn_no）"
                min-width="200"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="mdtrtId"
                label="就诊ID（mdtrt_id）"
                min-width="200"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="msgid"
                label="报文ID（msgid）"
                min-width="280"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="setlId"
                label="结算ID（setl_id）"
                min-width="200"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column
                prop="statusObject"
                label="处理状态"
                min-width="110"
                fixed="right"
            >
                <template #default="scope">
                    <span v-if="!!scope.row.statusObject" :style="{ color: scope.row.statusObject.color }">{{ scope.row.statusObject.name }}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                min-width="210"
                fixed="right"
            >
                <template #default="scope">
                    <el-button
                        size="small"
                        type="primary"
                        plain
                        :loading="scope.row.loadingHandle"
                        @click="onClickHandle(scope.row)"
                    >
                        处理
                    </el-button>
                    <el-tooltip
                        effect="dark"
                        content="请在研发同学的指导下使用该功能"
                        placement="top-start"
                    >
                        <el-button
                            size="small"
                            type="primary"
                            plain
                            :loading="scope.row.loadingEditing"
                            @click="onClickEdit(scope.row)"
                        >
                            修改
                        </el-button>
                    </el-tooltip>
                    <el-tooltip
                        effect="dark"
                        content="处理状态标记为：已处理"
                        placement="top-start"
                    >
                        <el-button
                            size="small"
                            type="primary"
                            plain
                            :loading="scope.row.loadingMark"
                            :disabled="scope.row.disabledMark"
                            @click="onClickMark(scope.row)"
                        >
                            标记
                        </el-button>
                    </el-tooltip>
                </template>
            </el-table-column>
        </el-table>
        <div class="footer-box">
            <span>异常结算笔数：{{ pageModel.params.total }}</span>
            <el-pagination
                v-model:current-page="pageModel.params.page"
                :page-size="pageModel.params.pageSize"
                :total="pageModel.params.total"
                background
                layout="total, prev, pager, next"
            ></el-pagination>
        </div>

        <dialog-pre-settle-edit
            v-if="dialogModelPreSettleEdit.visible.value"
            v-model="dialogModelPreSettleEdit.visible.value"
            :device-code="props.deviceCode"
            :item="queryResponse.selectedItem"
            @success="onEditSuccess"
        ></dialog-pre-settle-edit>
    </el-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { createErrorSettleController } from './controller';
import * as utils from '../../../common/utils';

import DialogPreSettleEdit from './dialog-pre-settle-edit/index.vue';

const props = defineProps({
    deviceCode: {
        type: String,
        default: '',
    },
    modelValue: {
        type: Number,
        default: 0,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
]);

const isShowDialogErrorSettle = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModelQuery,
    dialogModelPreSettleEdit,
    pageModel,
    toolsParams,
    queryResponse,
    showDataList,
    createParams,
    requestErrorSettleData,
    requestErrorSettleHandle,
    requestErrorSettleMark,
} = createErrorSettleController(props);

/**
 * 当点击查询时
 * <AUTHOR>
 * @date 2024-08-07
 */
const onClickQuery = async () => {
    if (loadingModelQuery.loading.value) {
        return;
    }
    loadingModelQuery.setLoading(true);
    const params = createParams();
    const response = await requestErrorSettleData(params);
    loadingModelQuery.setLoading(false);
    if (response.status === false) {
        return ElMessage.error('查询失败: ' + response.message);
    }
    queryResponse.originData = response.data;
};

/**
 * 当点击处理异常数据时
 * <AUTHOR>
 * @date 2024-11-27
 * @param {Object} item
 */
const onClickHandle = async (item: any) => {
    if (item.status === 10 || item.status === 30) {
        const confirmResponse = await utils.messageConfirm('该笔异常结算已经处理，请确认是否需要再次处理', '提示', {
            type: 'warning',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
        });
        if (confirmResponse.status === false) {
            return confirmResponse;
        }
    }
    item.loadingHandle = true;
    const handleResponse = await requestErrorSettleHandle(item);
    item.loadingHandle = false;
    if (handleResponse.status === false) {
        return ElMessage.error('处理失败: ' + handleResponse.message);
    }
    ElMessage.success('处理成功');
    onClickQuery();
};

/**
 * 当点击预结算数据修改
 * <AUTHOR>
 * @date 2024-11-27
 * @param {Object} item
 */
const onClickEdit = async (item: any) => {
    queryResponse.selectedItem = item;
    dialogModelPreSettleEdit.show();
};

/**
 * 当点击标记已处理
 * <AUTHOR>
 * @date 2024-11-27
 * @param {Object} item
 */
const onClickMark = async (item: any) => {
    item.loadingMark = true;
    const markResponse = await requestErrorSettleMark(item);
    item.loadingMark = false;
    if (markResponse.status === false) {
        return ElMessage.error('标记失败: ' + markResponse.message);
    }
    ElMessage.success('标记成功');
    onClickQuery();
};
    
/**
 * 当编辑数据成功时
 * <AUTHOR>
 * @date 2024-11-27
 */
const onEditSuccess = async () => {
    onClickQuery();
};
</script>

<style lang="scss">
    .social-module__feature__dialog-error-settle {
        width: 1200px !important;

        .el-dialog__body {
            padding-top: 8px;
        }

        .tools-wrapper {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .el-table {
            margin-top: 8px;

            .el-table__empty-text {
                margin-top: 150px;
            }
        }

        .footer-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 16px;

            > span {
                color: #606266;
            }

            .el-pagination {
                margin-top: 0;
            }
        }
    }
</style>