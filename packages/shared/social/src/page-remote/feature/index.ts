/*
 * <AUTHOR> 
 * @DateTime 2024-08-07 19:34:04 
 */
import DialogXiaomiQzj from './dialog-xiaomi-qzj/index.vue';
import DialogGodMode from './dialog-god-mode/index.vue';
import DialogHospitalEdit from './dialog-hospital-edit/index.vue';
import DialogNetwork from './dialog-network/index.vue';
import DialogNetworkTest from './dialog-network-test/index.vue';
import DialogLogsDownload from './dialog-logs-download/index.vue';
import DialogCustomJs from './dialog-custom-js/index.vue';
import DialogLiangdingAccount from './dialog-liangding-account/index.vue';
import DialogDictionary from './dialog-dictionary/index.vue';
import DialogDictQuery from './dialog-dict-query/index.vue';
import DialogLimitPrice from './dialog-limit-price/index.vue';
import DialogSelfRatio from './dialog-self-ratio/index.vue';
import DialogHospitalInfo from './dialog-hospital-info/index.vue';
import DialogSettleInfo from './dialog-settle-info/index.vue';
import DialogMedicalInfo from './dialog-medical-info/index.vue';
import DialogErrorSettle from './dialog-error-settle/index.vue';
import DialogCheckingHelp from './dialog-checking-help/index.vue';
import DialogLiquidationHelp from './dialog-liquidation-help/index.vue';
import DialogCheckingDetail from './dialog-checking-detail/index.vue';
import DialogSettleData from './dialog-settle-data/index.vue';
import DialogInventory from './dialog-inventory/index.vue';
import DialogInventoryStat from './dialog-inventory-stat/index.vue';
import DialogFamilySettleData from './dialog-family-settle-data/index.vue';
import DialogThirdpartClear from './dialog-thirdpart-clear/index.vue';
import DialogDeleteSettleData from './dialog-delete-settle-data/index.vue';
import DialogErrorSettleReset from './dialog-error-settle-reset/index.vue';
import DialogInventoryCorrection from './dialog-inventory-correction/index.vue';

export default [
    {
        id: 'xiaomi-qzj',
        name: '前置机',
        Component: DialogXiaomiQzj,
        checkSocialReg: true, // 检查医保地区
    },
    {
        id: 'god-mode',
        name: 'God模式控制',
        Component: DialogGodMode,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
    },
    {
        id: 'hospital-edit',
        name: '机构修改控制',
        Component: DialogHospitalEdit,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
    },
    {
        id: 'network-test',
        name: '网络调试',
        Component: DialogNetworkTest,
    },
    {
        id: 'network',
        name: '专网检测',
        Component: DialogNetwork,
        checkSocialReg: true, // 检查医保地区
    },
    {
        id: 'logs-download',
        name: '本地日志下载',
        Component: DialogLogsDownload,
    },
    {
        id: 'custom-js',
        name: '自定义脚本',
        Component: DialogCustomJs,
    },
    {
        id: 'liangding-account',
        name: '两定账号',
        Component: DialogLiangdingAccount,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        show: (params: any) => {
            if (params.region.startsWith('shanxi_')) {
                return true;
            }
            if (params.region.startsWith('heilongjiang_')) {
                return true;
            }
            if (params.region.startsWith('neimenggu_')) {
                return true;
            }
            if (params.region.startsWith('zhejiang_')) {
                return true;
            }
            if (params.region === 'chongqing_gb') {
                return true;
            }
            return false;
        },
    },
    {
        id: 'dictionary',
        name: '字典表查询',
        Component: DialogDictionary,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'dict-query',
        name: '医保目录查询',
        Component: DialogDictQuery,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'limit-price',
        name: '限价目录查询',
        Component: DialogLimitPrice,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'self-ratio',
        name: '自付比例查询',
        Component: DialogSelfRatio,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'hospital-info',
        name: '机构信息查询',
        Component: DialogHospitalInfo,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'settle-info',
        name: '结算信息查询',
        Component: DialogSettleInfo,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'medical-info',
        name: '就诊信息查询',
        Component: DialogMedicalInfo,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'error-settle',
        name: '异常结算数据',
        Component: DialogErrorSettle,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'checking-help',
        name: '协助对账',
        Component: DialogCheckingHelp,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'liquidation-help',
        name: '协助清算',
        Component: DialogLiquidationHelp,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'checking-detail',
        name: '明细对账',
        Component: DialogCheckingDetail,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'settle-data',
        name: '中心结算数据',
        Component: DialogSettleData,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'family-settle-data',
        name: '家庭医生签约结算数据',
        Component: DialogFamilySettleData,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.region === 'liaoning_shenyang') {
                return true;
            }
            return false;
        },
    },
    {
        id: 'thirdpart-clear',
        name: '三方数据清理',
        Component: DialogThirdpartClear,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'delete-settle-data',
        name: '删除结算数据',
        Component: DialogDeleteSettleData,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'error-settle-reset',
        name: '异常结算状态重置',
        Component: DialogErrorSettleReset,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
        show: (params: any) => {
            if (params.isNational) {
                return true;
            }
            return false;
        },
    },
    {
        id: 'inventory',
        name: '进销存上报查询',
        Component: DialogInventory,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
    },
    {
        id: 'inventory-stat',
        name: '进销存统计查询',
        Component: DialogInventoryStat,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
    },
    {
        id: 'inventory-correction',
        name: '库存校正',
        Component: DialogInventoryCorrection,
        checkSocialReg: true, // 检查医保地区
        checkSoicalOpe: true, // 检查医保开通
        checkSocialCom: true, // 检查医保电脑
    },
];