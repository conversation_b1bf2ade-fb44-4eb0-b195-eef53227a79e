/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import { computed, reactive } from 'vue';
import * as utils from '../../../common/utils';

import createPageModel from '../../../model/page';
import createLoadingModel from '../../../model/loading';

export const createFamilySettleDataController = (props: any) => {
    const pageModel = createPageModel();
    const loadingModelQuery = createLoadingModel();

    const toolsParams = reactive({
        dataRange: [
            dayjs().startOf('month').format('YYYY-MM-DD'),
            dayjs().endOf('month').format('YYYY-MM-DD'),
        ],
    });

    const queryResponse = reactive({
        originData: <any> null,
    });

    // 匹配数据
    let cacheToolsParams = {};
    const mateDataList = computed(() => {
        const dataList = queryResponse.originData?.output?.setlinfo || [];
        pageModel.setTotal(dataList.length);
        if (!utils.isEqual(cacheToolsParams, toolsParams)) {
            pageModel.setPage(1);
            cacheToolsParams = utils.cloneDeep(toolsParams);
        }
        return dataList;
    });

    // 汇总数据
    const summaryData = computed(() => mateDataList.value.reduce((summaryData: any, item: any) => {
        summaryData.medfeeSumamt = utils.add(summaryData.medfeeSumamt, item.medfee_sumamt);
        summaryData.fundPaySumamt = utils.add(summaryData.fundPaySumamt, item.fund_pay_sumamt);
        summaryData.acctPay = utils.add(summaryData.acctPay, item.acct_pay);
        summaryData.cashPayamt = utils.add(summaryData.cashPayamt, item.cash_payamt);
        return summaryData;
    }, {
        medfeeSumamt: 0,
        fundPaySumamt: 0,
        acctPay: 0,
        cashPayamt: 0,
    }));

    // 展示数据
    const showDataList = computed(() => {
        const { sIndex, eIndex } = pageModel.createSliceParams();
        return mateDataList.value
                        .slice(sIndex, eIndex)
                        .map((item: any) => {
                            const itemInfo = {
                                psnName: item.psn_name, // 人员姓名
                                medfeeSumamt: utils.moneyStr(item.medfee_sumamt), // 医疗费用总额
                                fundPaySumamt: utils.moneyStr(item.fund_pay_sumamt), // 基金支付总额
                                acctPay: utils.moneyStr(item.acct_pay), // 个人账户总额
                                cashPayamt: utils.moneyStr(item.cash_payamt), // 现金支付总额
                                psnNo: item.psn_no, // 人员编号
                                mdtrtId: item.mdtrt_id, // 就诊ID
                                setlId: item.setl_id, // 结算ID
                                setlTime: item.setl_time, // 结算时间
                                refdSetlFlag: (() => {
                                    if (item.refd_setl_flag === '0') {
                                        return '否';
                                    }
                                    if (item.refd_setl_flag === '1') {
                                        return '是';
                                    }
                                    return '';
                                })(), // 退费结算标志
                            };
                            return itemInfo;
                        });
    });

    /**
     * 创建查询参数
     * <AUTHOR>
     * @date 2025-04-23
     * @returns {Object}
     */
    const createParams = () => {
        const params = {
            data: {
                begntime: toolsParams.dataRange[0],
                endtime: toolsParams.dataRange[1],
            },
        };
        return params;
    };

    /**
     * 查询家庭签约结算数据
     * <AUTHOR>
     * @date 2025-04-23
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestFamilySettleData = async (params: any) => {
        const js = `
            const params = ${JSON.stringify(params)}
            response = await window.$national.protocol.NationalSocialSecurity.instance.queryFamilyDoctorContractSettleInfo(params)
        `;
        const response = await utils.remoteRunJs(props.deviceCode, js);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        pageModel,
        loadingModelQuery,
        toolsParams,
        queryResponse,
        shortcuts: utils.createShortcuts(),
        showDataList,
        summaryData,
        createParams,
        requestFamilySettleData,
    };
};