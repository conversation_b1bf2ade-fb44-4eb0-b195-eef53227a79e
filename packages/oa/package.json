{"name": "abc-smart-oa", "version": "0.0.0", "scripts": {"dev": "vite --mode loc", "build": "node ./build.js", "preview": "vite preview", "swagger": "node ./tools/swagger2ts/index.js && eslint --fix ./src/api/*.ts", "tsc": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "oa:tag": "git abc tag config static-oa && git abc tag create ", "cms:tag": "git abc tag config static-cms && git abc tag create "}, "dependencies": {"@abc-oa/common": "workspace:*", "@abc-oa/components": "workspace:*", "@abc-oa/social": "workspace:*", "@abc-oa/styles": "workspace:*", "@abc-oa/utils": "workspace:*", "@element-plus/icons-vue": "~2.0.10", "@svgdotjs/svg.js": "~3.1.2", "@tiptap/core": "2.1.12", "@tiptap/extension-color": "2.1.12", "@tiptap/extension-font-family": "2.1.12", "@tiptap/extension-highlight": "2.1.12", "@tiptap/extension-horizontal-rule": "2.1.12", "@tiptap/extension-image": "~2.1.13", "@tiptap/extension-link": "2.1.12", "@tiptap/extension-subscript": "2.1.12", "@tiptap/extension-superscript": "2.1.12", "@tiptap/extension-text-align": "2.1.12", "@tiptap/extension-text-style": "2.1.12", "@tiptap/extension-typography": "2.1.12", "@tiptap/extension-underline": "2.1.12", "@tiptap/pm": "2.1.13", "@tiptap/starter-kit": "2.1.12", "@tiptap/vue-3": "2.1.12", "@tool/anti-crawler": "1.1.8", "@tool/date": "~0.1.3", "@tool/utils": "~0.8.2", "@vant/touch-emulator": "~1.3.2", "@vueup/vue-quill": "~1.0.0-beta.8", "@wangeditor/editor": "~5.1.22", "@wangeditor/editor-for-vue": "~5.1.12", "axios": "~0.24.0", "chart.js": "^4.4.9", "codemirror": "~6.0.1", "dayjs": "^1.11.8", "echarts": "~5.3.3", "element-plus": "2.2.6", "eslint-plugin-abc": "~0.3.1", "js-cookie": "~3.0.5", "monaco-editor": "~0.33.0", "pinia": "~2.0.8", "prosemirror-utils": "~1.2.1-0", "qrcode": "~1.5.0", "querystring": "~0.2.1", "quill-blot-formatter": "~1.0.5", "quill-image-uploader": "~1.2.2", "remixicon": "~3.5.0", "signature_pad": "~4.1.4", "vant": "3.3.7", "vconsole": "~3.11.1", "vue": "~3.2.25", "vue-codemirror": "~6.1.1", "vue-cropper": "1.0.5", "vue-json-viewer": "3", "vue-router": "4.0.12", "vuedraggable": "~4.1.0", "xlsx": "~0.18.5"}, "devDependencies": {"@types/mockjs": "~1.0.4", "@types/node": "~17.0.1", "@types/qrcode": "~1.4.2", "@typescript-eslint/eslint-plugin": "~5.1.0", "@typescript-eslint/parser": "~5.1.0", "@vitejs/plugin-vue": "~2.0.0", "@vitejs/plugin-vue-jsx": "~1.3.3", "@vue/eslint-config-prettier": "~6.0.0", "@vue/eslint-config-typescript": "~9.1.0", "abc-fed-build-tool": "0.5.0", "autoprefixer": "~10.4.2", "connect-history-api-fallback": "~1.6.0", "ejs": "~3.1.6", "eslint": "^7.2.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-typescript": "^0.14.0", "eslint-plugin-vue": "^7.20.0", "express": "~4.17.2", "express-serve-static-core": "~0.1.1", "fs-extra": "~10.0.0", "got": "~11.8.2", "mockjs": "~1.1.0", "path": "~0.12.7", "postcss-pxtorem": "~6.0.0", "prettier": "~2.5.1", "prosemirror-dev-toolkit": "~1.1.5", "typescript": "^5.5.3", "vite": "2.7.2", "vite-plugin-history": "~1.0.2", "vite-plugin-html": "~3.2.0", "vite-plugin-imp": "~2.0.11", "vite-plugin-mock": "~2.9.6", "vite-plugin-vconsole": "~1.1.1", "vue-eslint-parser": "~7.11.0", "vue-tsc": "^2.1.6"}}