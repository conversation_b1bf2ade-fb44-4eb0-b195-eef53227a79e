# 智能 OA 平台
> 基于：Vue 3 + Typescript + Vite + ElementPlus

## 基础页面
### 查询表格 QueryTable


### 脚本任务

### 本地开发登录
localhost:3000
进入login页面后先

# **不要扫码！！！不要扫码！！！不要扫码！！！**

![img.png](img.png)
左键按住vConsole图标，拖拽，可以看到一个switch，打开后再扫码
![img_1.png](img_1.png)


## 数据协议

### 分页
request
```javascript
{
    offset: 0,
    limit: 10,
}
```
response
```javascript
{
    rows: [
        {
            id: 123,
            name: 'abc',
            ...
        }
    ], 
    pagination: {
        total: 100,
        offset: 0, 
        limit: 10,
    }
}
```

### 选项列表
> 用于为前端选项列表提供数据源，例如：人员列表、门店列表等 

request
```javascript
{
}
```

response
```javascript
{
    label: '', // 选项名 
    value: '', // 选项值
    ..., // 其他参数，例如 py 等
}
```

### 执行脚本任务
> 请求执行后，后台应该会生成一个该次执行的记录，后续会根据该记录 id 查询该任务状态

request
```javascript
{
    taskId: '', // 根据情况再命名，标志一个脚本任务
    params: { // 该任务需要的参数
        
    }
}
```
response
```javascript
{
    processId: '', // 本次执行记录的 id 
}
```
