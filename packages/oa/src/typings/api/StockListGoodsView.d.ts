declare namespace  AbcAPI {
    
    type StockListGoodsView = {    
        //是否子店定价这个之前放到goods里面，目前来说都是门店自主定价，没有单独药品还能自主定价的
        allowSubClinicSetPrice:boolean    
        //中药类型
        cMSpec:string    
        //药品总店的禁用状态
        chainDisable:number    
        //价格三元组
        chainPackagePrice:number    
        //价格三元组
        chainPiecePrice:number    
        //总店药品禁用状态
        chainV2DisableStatus:number    
        //药品禁用状态
        disable:number    
        //是否拆零
        dismounting:number    
        //账面库存 ：当前库存量 后台拼接（药品物资列表那里展示的是这个库存）
        dispGoodsCount:string    
        //拼接好的名字
        displayName:string    
        //后台已经组装好的药品规格字符串
        displaySpec:string    
        //最近过期的有效期是否告警
        expiredWarnFlag:boolean    
        //商品goodsId 兼容
        goodsId:string    
        //后台已经组装好药品类型
        goodsTypeName:string    
        //商品goodsId
        id:string    
        //进税
        inTaxRat:number    
        //价格三元组,null表示没有
        lastPackageCostPrice:number    
        //最近供应商
        lastSupplierName:string    
        //当前锁库库存量 后台拼接好 有锁库库存这个字符串为非空
        lockingDispGoodsCount:string    
        //生产厂家短名
        manufacturer:string    
        //国药准字
        medcineNpmn:string    
        //cadn
        medicineCadn:string    
        //最近过期的有效期,null表示没有
        minExpiryDate:string    
        //商品商品名
        name:string    
        //利润率是否告警
        negativeProfitWarnFlag:boolean    
        //销税
        outTaxRat:number    
        //库存量 大单位
        packageCount:number    
        //价格三元组
        packagePrice:number    
        //单位
        packageUnit:string    
        //库存量 小单位
        pieceCount:number    
        //pieceNum
        pieceNum:number    
        //价格三元组
        piecePrice:number    
        //单位
        pieceUnit:string    
        //柜号
        position:string    
        //利润率,null表示没有
        profitRat:number    
        //禁止销售的库存 大单位 有禁止销售 这个字段才有
        prohibitStock:number    
        //日均销量,null表示没有
        recentAvgSell:number    
        //国家医保对码视图
        shebaoNationalView?:GoodsShebaoView    
        //医保支付方式 0 正常支付  1 自费支付 2 禁止医保支付
        shebaoPayMode:number    
        //医保支付方式 0 正常支付  1 自费支付 2 禁止医保支付
        shebaoPayModeName:string    
        //药品编码
        shortId:string    
        
        shortageWarnFlag:boolean    
        //药品子类型
        subType:number    
        //药品总成本,null表示没有
        totalCost:number    
        //周转天数 整数,null表示没有
        turnoverDays:number    
        
        turnoverDaysWarnFlag:boolean    
        //药品类型
        type:number    
        //typeId ->(type,subType,cMSpec三元组的id)
        typeId:number    
        //门店药品禁用状态
        v2DisableStatus:number    
    }
}
