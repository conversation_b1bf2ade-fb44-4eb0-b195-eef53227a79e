declare namespace  AbcAPI {
    
    type CreatedDataDto = {    
        //反馈人id
        clientId?:string    
        //反馈人系统内部id
        employeeId?:string    
        //反馈人姓名
        clientName?:string    
        //反馈人openId
        openId?:string    
        //反馈人手机号
        mobile?:string    
        //反馈门店id
        clinicId?:string    
        //门店版本
        edition?:string    
        //门店产品线
        hisType?:number    
        //建单销售
        userId?:string    
        //问题概述
        question?:string    
        //处理人
        owner?:string    
        //处理人真实姓名
        dealerName?:string    
        //是否来源于群聊
        isChatAccount?:number    
        //群聊消息记录
        msgList?:Array<any>    
        //是否紧急问题(0 非紧急， 1是紧急)
        isEmergency?:number    
        //问题分类标签
        tagList?:Array<any>    
        //问题分类标签名称列表
        tagNameList?:Array<any>    
        //反馈时间  yyyy-MM-dd HH:mm:ss
        feedbackTime?:string    
        //企业微信ID
        qwCorpId?:string    
        //群聊会话ID
        conversationId?:string    
        //群聊名称
        roomName?:string    
        //企业微信user id
        qwUserId?:string    
        //企业微信user name
        qwUserName?:string    
        //补充信息
        chatInputValue?:Array<any>    
        //管理员信息
        adminInfo?:any    
        //工单类型：（0：bug 1：需求 2：实施 4：支持）
        type?:number    
        //工单来源：（0：1v1 1：群聊 2：客户于系统提单 3：内部人员提单 90: 其他）
        fromWay?:number    
        //草稿： 0: 不是草稿 1: 是草稿
        isDraft?:number    
    }
}
