declare namespace  AbcAPI {
    
    type CreateStockInOrderReq = {    
        //[总部给哪个门店入库]入库单入库门店ID
        clinicId?:string    
        //入库单备注
        comment?:string    
        
        id?:number    
        //入库草稿单id
        inOrderDraftId?:number    
        
        lastModifiedDate?:string    
        
        list?:Array<CreateStockInItemViewReq>    
        //商城采购入库的订单ID
        mallOrderId?:number    
        //入库单关联的随货单号
        outOrderNo?:string    
        //药房号,不传默认为0号药房
        pharmacyNo?:number    
        //药房类型目前这一期类型和no一一对应,入库和前面的药品物资都对齐使用药房类型
        pharmacyType?:number    
        
        reqMd5Key?:string    
        //入库单供应商Id
        supplierId?:string    
    }
}
