declare namespace  AbcAPI {
    
    type ChangeClinicEditionPeriodContentRequest = {    
        //备注截图
        attachments?:Array<any>    
        //生效开始时间 (yyyy-MM-dd)
        beginDate?:string    
        //门店id
        clinicId?:string    
        //门店名称，前端可以不传，后台会获取
        clinicName?:string    
        //订单创建时间 (yyyy-MM-dd)
        createdDate?:string    
        //生效结束时间 (yyyy-MM-dd)
        endDate?:string    
        //修改前生效开始时间 (yyyy-MM-dd)
        oldBeginDate?:string    
        //修改前生效结束时间 (yyyy-MM-dd)
        oldEndDate?:string    
        //备注
        remark?:string    
    }
}
