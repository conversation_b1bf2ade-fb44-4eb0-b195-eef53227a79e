declare namespace  AbcAPI {
    
    type UpdateAllinPayReq = {    
        //卡户银行卡号，不能为空
        bankCard?:string    
        //集团号appId
        blocAppId?:string    
        //集团号id
        blocId?:string    
        //业务场景，线下支付0，线上支付10
        businessScene?:number    
        //商户号，不能为空
        cusId?:string    
        //开户行，不能未空
        depositBank?:string    
        //是否为单独的集团，如果为1，blocId和blocAppId不能为空
        isSingleBloc?:number    
        //签名key
        md5Key?:string    
        //商户简称，不能为空
        merchantShortName?:string    
        //门店id, 不能为空
        organId?:string    
        //终端地址：四川省-成都市-高新区-中海国际D座，不能为空
        termAddress?:string    
    }
}
