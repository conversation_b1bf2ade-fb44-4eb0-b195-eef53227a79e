declare namespace  AbcAPI {
    
    type ZoneTaskDetail = {    
        //regionCount的类型，1 比例 2 数量,目前写1
        countType:number    
        //本次灰度步骤创建时间
        created:string    
        //本次灰度步骤完成执行时间
        finished:string    
        //灰度步骤主键ID
        id:number    
        //灰度步骤名字
        name:string    
        //本次灰度步骤实际放量的连锁数量
        realRegionCount:number    
        //本次灰度步骤放量的比例 1-100
        regionCount:number    
        //灰度步骤开始执行的时间
        started:string    
        //本次灰度步骤状态 10 进行中 20 完成
        status:number    
    }
}
