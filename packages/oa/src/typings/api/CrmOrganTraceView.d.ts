declare namespace  AbcAPI {
    
    type CrmOrganTraceView = {    
        //内容{@linkCrmOrganTrace.FollowUpRecord} {@linkCrmOrganTrace.LifeCircle} {@linkCrmOrganTrace.ClueInformation}
        content?:JsonNode    
        //创建时间
        created:string    
        //创建人
        createdByName:string    
        //id
        id:string    
        //0生命周期1跟进记录2线索信息(线索动态){@linkCrmOrganTrace.Type}
        type:number    
    }
}
