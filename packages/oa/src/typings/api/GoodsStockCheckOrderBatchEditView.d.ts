declare namespace  AbcAPI {
    
    type GoodsStockCheckOrderBatchEditView = {    
        //盘点药房
        checkPharmacy?:GoodsPharmacyView    
        //盘点单盘点的评论列表,最近的是最后一个
        comment?:Array<CommentDBJson>    
        //盘点单创建时间
        createdDate:string    
        //盘点单创建人
        createdUser?:EmployeeView    
        //盘点单Id
        id:string    
        //盘点单盘点药品的总数量
        kindCount:number    
        //盘点的商品列表
        list?:Array<GoodsStockCheckOrderItemBatchEditView>    
        //盘点单历史，降序
        logs?:Array<GoodsStockCheckOrderLogView>    
        //盘点单可读编号
        orderNo:string    
        //盘点门店
        organ?:OrganView    
        //盘点单状态，盘点单状态 0 未知初始状态，10 新建待审批, 20 审批被拒绝 30 完成(审批通过) 40撤回
        status:number    
        //盘点单状态字符串，待审批，拒绝，完成，撤回等
        statusName:string    
        //本次盘点范围，为空表示全部或不限定范围
        stockCheckScope?:StockCheckScope    
    }
}
