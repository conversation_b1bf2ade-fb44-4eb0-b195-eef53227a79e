declare namespace  AbcAPI {
    
    type UpgradeClinicContentRequest = {    
        //折扣金额
        adjustmentPrice?:number    
        //生效开始时间 (yyyy-MM-dd)
        beginDate?:string    
        //门店id
        clinicId?:string    
        //门店名称，前端可以不传，后台会获取
        clinicName?:string    
        //抵扣金额
        deductionFee?:number    
        //优惠券金额
        discountPrice?:number    
        //购买店版本id (基础版 10, 专业版 20, 旗舰版 30, 大客户版 40)
        editionId?:string    
        //生效结束时间 (yyyy-MM-dd)
        endDate?:string    
        //使用优惠券列表
        promotions?:Array<UpgradeClinicContentRequestPromotion>    
        //应收费用
        receivableFee?:number    
        //收款账户id (字节流 1, 字节星球 2)
        receiveAccountId?:number    
        //备注
        remark?:string    
        //合计费用
        totalPrice?:number    
        //单位数量 (N年)
        unitCount?:number    
    }
}
