declare namespace  AbcAPI {
    
    type ClinicCustomerEditionOrderCalculateRsp = {
        //增购账号抵扣金额
        accountDeductionFee:number
        //增购账号费用详情
        accountOrderFeeInfo?:ClinicEditionOrderCalculateRspOrderFeeInfo
        //可以购买的版本id列表
        availableEditionIds?:Array<any>
        //可以使用的优惠券列表
        availablePromotions?:Array<ClinicEditionOrderCalculateRspPromotion>
        //版本基础账号数
        basicEmployeeCount:number
        //开始时间
        beginDate:string
        //bpENtrance 需要诊所信息
        clinic?:ClinicCustomerBindOrganView
        //抵扣费用
        deductionFee:number
        //折扣
        discountPrice:number
        //版本费用抵扣金额
        editionDeductionFee:number
        //购买店版本id
        editionId:string
        //版本费用详情
        editionOrderFeeInfo?:ClinicEditionOrderCalculateRspOrderFeeInfo
        //版本总费用
        editionOrderTotalPrice:number
        //结束时间
        endDate:string
        //赠送天数
        giftDays:number
        //订单id
        id:string
        //是否可以按历史价格续费 2023年2月1日前购买的门店并且未使用一次原价续费机会
        isCanReBuyWithHistoryPrice:number

        maxAdjustment?:ClinicEditionMaxAdjustment

        maxAdjustmentFee:number
        //最晚生效时间
        maxBeginDate:string
        //最晚结束时间
        maxEndDate:string
        //最少应收金额
        minReceivableFee:number
        //预先支付的金额
        prePaidFee:number
        //应收费用
        receivableFee:number
        //SOP阶段续费订单关联的版本订单
        sopRenewRelateEditionOrders?:Array<QWClinicEditionOrderAbstract>
        //总价
        totalPrice:number
        //订单类型（1: 首购；2: 续费；3: 升级；4: 降级）
        type:number
        //单位
        unit:string
        //单位数量
        unitCount:number
        //单位定价
        unitPrice:number    
    }
}
