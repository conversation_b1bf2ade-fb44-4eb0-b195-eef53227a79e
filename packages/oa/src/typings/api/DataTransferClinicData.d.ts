declare namespace  AbcAPI {
    
    type DataTransferClinicData = {    
        
        id:number    
        //导出连锁id
        exportChainId:string    
        //导出门店id
        exportClinicId:string    
        //导入连锁id
        importChainId:string    
        //导入门店id
        importClinicId:string    
        //迁移类型
        types:Array<any>    
        //免责协议客户签章阿里云连接列表
        links:Array<any>    
        //备注
        remark:string    
        //是否删除
        isDeleted:number    
        //创建人
        createdBy:string    
        
        created:any    
        //最后修改人
        lastModifiedBy:string    
        
        lastModified:any    
    }
}
