declare namespace  AbcAPI {
    
    type CreateStockTransOrderReviewV1Req = {    
        
        comment?:string    
        //入库单的上次修改时间[审核，确认这个字段都有用]
        lastModifiedDate?:string    
        //调出方确认的时候可以修改
        list?:Array<CreateStockTransItemViewReq>    
        
        pass?:boolean    
        //调入门店的药房号 在调入方确认入库的时候，可以选择换入库药房
        transInPharmacyNo?:number    
        //调出门店的药房号 在调出方确认出库的时候，可以选择换药房
        transOutPharmacyNo?:number    
    }
}
