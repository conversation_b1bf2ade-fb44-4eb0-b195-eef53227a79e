declare namespace  AbcAPI {
    
    type GoodsStockCheckOrderView = {    
        //盘点后总成本(数量*成本价)
        afterCostAmount:number    
        //盘点后总成本(数量*成本价)(除开进销税)
        afterCostAmountExcludingTax:number    
        //盘点后总数量
        afterCount:number    
        //盘点后可以买的总价钱(数量*售价)
        afterSaleAmount:number    
        //盘点后可以买的总价钱(数量*售价)(除开进销税)
        afterSaleAmountExcludingTax:number    
        //盘点前总成本(数量*成本价)
        beforeCostAmount:number    
        //盘点前总成本(数量*成本价)(除开进销税)
        beforeCostAmountExcludingTax:number    
        //盘点前总数量
        beforeCount:number    
        //盘点前可以买的总价钱(数量*售价)
        beforeSaleAmount:number    
        //盘点前可以买的总价钱(数量*售价)(除开进销税)
        beforeSaleAmountExcludingTax:number    
        //盘点单盘点的评论列表,最近的是最后一个
        comment?:Array<CommentDBJson>    
        //盘点单创建时间
        createdDate:string    
        //盘点单创建人
        createdUser?:EmployeeView    
        //盘点单Id
        id:number    
        //盘点单盘点药品的总数量
        kindCount:number    
        //盘点的商品列表
        list?:Array<GoodsStockCheckOrderItemView>    
        //盘点单历史，降序
        logs?:Array<GoodsStockCheckOrderLogView>    
        //盘点单可读编号
        orderNo:string    
        //盘点门店
        organ?:OrganView    
        //盘点药房
        pharmacy?:SimpleGoodsPharmacyView    
        //盘点单状态，盘点单状态 0 未知初始状态，10 新建待审批, 20 审批被拒绝 30 完成(审批通过) 40撤回
        status:number    
        //盘点单状态 0 未知初始状态，10 新建待审批, 20 审批被拒绝 30 完成(审批通过) 40撤回
        statusName:string    
        //本次盘点范围，为空表示全部或不限定范围
        stockCheckScope?:StockCheckScopeWithCustomTypeName    
        //盘点总成本的变更量
        totalCostPriceChange:number    
        //盘点总售价的变更量
        totalPriceChange:number    
    }
}
