declare namespace  AbcAPI {
    
    type OrganSimpleView = {    
        //门店城市
        addressCityName:string    
        //门店详细地址
        addressDetail:string    
        //门店县区
        addressDistrictName:string    
        //门店省份
        addressProvinceName:string    
        //版本开始时间
        beginDate:string    
        //门店创建时间
        createdDate:string    
        //门店版本
        editionId:number    
        //门店版本名称
        editionName:string    
        //版本结束时间
        endDate:string    
        //门店类型
        hisType:number    
        //门店ID
        id:string    
        //门店名称
        name:string    
        //节点类型：0:普通诊所；1：连锁诊所；2：连锁诊所子诊所
        nodeType:number    
    }
}
