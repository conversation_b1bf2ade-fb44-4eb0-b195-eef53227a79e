declare namespace  AbcAPI {
    
    type MallOrderInfoClientView = {    
        
        approvedCode:string    
        
        barCode:string    
        
        cadn:string    
        
        categoryId:string    
        
        categoryName:string    
        
        mallDealDiscountPrice:number    
        
        mallDealPurchaseCount:number    
        
        mallDealTotalPrice:number    
        
        mallDiscountPrice:number    
        
        mallOrderId:string    
        
        mallPurchaseCount:number    
        
        mallTotalPrice:number    
        
        manufacturer:string    
        
        maxSalesCount:number    
        
        minSalesCount:number    
        
        salesUnit:string    
        
        salesUnitPrice:number    
        
        shortId:string    
        
        skuGoodsId:string    
        
        skuGoodsName:string    
        
        skuStockCount:number    
        
        specification:string    
        
        spuGoodsId:string    
        
        status:number    
        
        stockInOrderStatus:number    
        
        stockInOrderStatusName:string    
        
        tradeName:string    
        
        transRatio:number    
        
        vendorName:string    
    }
}
