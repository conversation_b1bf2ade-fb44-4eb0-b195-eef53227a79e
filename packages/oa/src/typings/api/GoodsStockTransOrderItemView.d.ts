declare namespace  AbcAPI {
    
    type GoodsStockTransOrderItemView = {    
        //调拨单上填写的调拨大包数量 从调试来看，pc没用这个字段，用的是转后的packageCount
        applicationPackageCount:number    
        //调拨单上填写的调拨小包数量 从调试来看，pc没用这个字段，用的是转后的packageCount
        applicationPieceCount:number    
        //调拨goods的详细批次
        batchs?:Array<CreateStockTransBatchesView>    
        
        fromOrganStock?:GoodsStockValue    
        //快照goods
        goods?:GoodsSnapV3    
        //快照goods
        goodsId:string    
        
        lastModifiedDate:string    
        //后台吐给前端的协议：调拨的成本价 异价调拨就是创建调拨单上的成本价，同价调拨 未发生调拨前是用户输入的成本价，调拨发生后是最终的平均成本价 乱啊
        outPackageCostPrice:number    
        //总成本
        outTotalCostPrice:number    
        // 新加一个字段 用来标识这个调拨的成本价 以前协议的一个调拨单，随着调拨单把goods的所有批次信息一起拉出去了 前端自己算的成本价，现在stocks在需要的时候来拉取，从而需要后台有个返回成本价的地方 
        packageCostPrice:number    
        //调拨单转后的大包数量
        packageCount:number    
        //调拨单转后的小包数量
        pieceCount:number    
        
        toOrganStock?:GoodsStockValue    
        //老协议，存储为了兼容
        totalCostPrice:number    
    }
}
