declare namespace  AbcAPI {
    
    type RenewClinicContentRequestReq = {    
        //增购账号抵扣金额
        accountDeductionFee?:number    
        //增购账号费用详情
        accountOrderFeeInfo?:OrderFeeInfo    
        //折扣金额
        adjustmentPrice?:number    
        //降级时用户确认截图
        attachments?:Array<any>    
        //生效开始时间 (yyyy-MM-dd)
        beginDate?:string    
        //门店id
        clinicId?:string    
        //门店名称，前端可以不传，后台会获取
        clinicName?:string    
        //crm 机构id
        crmOrganId?:string    
        //crm 机构名称
        crmOrganName?:string    
        //抵扣金额
        deductionFee?:number    
        //优惠券金额
        discountPrice?:number    
        //版本费用抵扣金额
        editionDeductionFee?:number    
        //购买店版本id (基础版 10, 专业版 20, 旗舰版 30, 大客户版 40)
        editionId?:string    
        //版本费用详情
        editionOrderFeeInfo?:OrderFeeInfo    
        //版本总费用
        editionOrderTotalPrice?:number    
        //生效结束时间 (yyyy-MM-dd)
        endDate?:string    
        //诊所类型
        hisType?:number    
        //是否是SOP阶段续费 0:否 1:是
        isSopRenewPurchase?:number    
        //支付对象 10楚天云 0ABC
        payAccount?:number    
        //预付金额
        prePaidFee?:number    
        //使用优惠券列表
        promotions?:Array<RenewClinicContentRequestPromotion>    
        //应收费用
        receivableFee?:number    
        //收款账户id (字节流 1, 字节星球 2)
        receiveAccountId?:number    
        //备注
        remark?:string    
        //成单分享
        share?:string    
        //SOP阶段续费类型 0:一年改为两年 1:一年改为三年 2:两年改为三年
        sopRenewPurchaseType?:number    
        //SOP阶段续费关联的版本订单
        sopRenewRelateEditionOrders?:Array<QWClinicEditionOrderAbstract>    
        
        supportItems?:Array<SupportItemReq>    
        //合计费用
        totalPrice?:number    
        //单位数量 (N年)
        unitCount?:number    
    }
}
