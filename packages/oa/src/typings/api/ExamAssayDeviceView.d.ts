declare namespace  AbcAPI {
    
    type ExamAssayDeviceView = {    
        //设备 购买url
        buyUrl:string    
        //设备在门店的信息
        clinicInfos?:Array<ExamAssayDeviceClinicInfo>    
        //设备对接状态 0-未开始 10-对接中 20-已完成
        connectStatus:number    
        //设备实例化的id
        deviceId:string    
        
        deviceModelId:string    
        //设备型号状态 0-可见 99 不可见
        deviceModelStatus:number    
        //设备参数
        deviceParameters?:JsonNode    
        //设备类型 检验: [1临床检验, 2生化检验, 3免疫检验, 4微生物检验, 5pcr检验, 6未知分类] 检查: [0未知, 1CT, 2DR, 3CR, 4普通透视, 5心电图, 6骨密度, 7试光类]
        deviceType:number    
        //设备类型 检验: [1临床检验, 2生化检验, 3免疫检验, 4微生物检验, 5pcr检验, 6未知分类] 检查: [0未知, 1CT, 2DR, 3CR, 4普通透视, 5心电图, 6骨密度, 7试光类]
        deviceTypeName:string    
        //UUID
        deviceUuid:string    
        //检验设备扩展信息
        extendInfos?:ExamineDeviceModelExtendInfos    
        //检验Goods 子类型 1
        goodsSubType:number    
        //检验Goods 类型 3
        goodsType:number    
        //设备 IconURL
        iconUrl:string    
        //设备Id 雪花算法Id，String避免精度丢失.0未知设备
        id:string    
        //未知设备为1
        innerFlag:number    
        //设备 安装url
        installUrl:string    
        //本诊所已经实例化出来的模版Goods
        installedLocalGoodsList?:Array<GoodsItem>    
        //设备 生产商  迈瑞
        manufacture:string    
        //仪器型号
        model:string    
        //仪器名称
        name:string    
        //设备 备注信息
        remarks:string    
        //售价
        salePrice:number    
        //未安装的模版Goods列表
        unInstallStandardGoodsList?:Array<GoodsItem>    
        //设备用途类型（检验设备） 0 未知类型 1 血液分析 2 生化分析 3尿液分析 4免疫分析 5 微生物分析
        usageType:number    
        //设备用途类型（检验设备） 0 未知类型 1 血液分析 2 生化分析 3尿液分析 4免疫分析 5 微生物分析
        usageTypeName:string    
    }
}
