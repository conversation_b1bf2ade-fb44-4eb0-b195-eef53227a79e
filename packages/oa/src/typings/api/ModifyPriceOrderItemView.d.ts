declare namespace  AbcAPI {
    
    type ModifyPriceOrderItemView = {    
        //改后价格
        afterPackagePrice:number    
        //改后价格
        afterPiecePrice:number    
        //改前价格
        beforePackagePrice:number    
        //改前价格
        beforePiecePrice:number    
        //快照 改过价格后的
        goods?:GoodsSnapV3    
        
        id:string    
        //最近供应商名字 改价时的快照
        lastSupplierName:string    
        //改 基准数据 (基于进价/社保限价 调时记录的是最近入库成本价/社保限价)
        modifyBasePackagePrice:number    
        //改 基准数据 (基于进价/社保限价 调时记录的是最近入库成本价/社保限价)
        modifyBasePiecePrice:number    
        
        orderId:string    
        //快照成本价
        packageCostPrice:number    
        //改 动作类型   (单个goods还可以手动改，这里的opType和order表上opType可以不一样) 这个Goods 修改价格的类型 0 手动设置售价,1 按原进价进行比例调价，2按最近进价进行比例调价
        packageOpType:number    
        //改 比例(基于modifyBasePackagePrice的比例，不是基于改前价格beforePackagePrice的比例)
        packageUpPercent:number    
        //改 动作类型  (单个goods还可以手动改，这里的opType和order表上opType可以不一样) 这个Goods 修改价格的类型 0 手动设置售价,1 按原进价进行比例调价，2按最近进价进行比例调价
        pieceOpType:number    
        //改 比例(基于modifyBasePiecePrice的比例，不是基于改前价格beforePiecePrice的比例)
        pieceUpPercent:number    
        //利润率
        profitRat:number    
    }
}
