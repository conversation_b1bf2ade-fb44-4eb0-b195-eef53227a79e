declare namespace  AbcAPI {
    
    type GetNonStockGoodsListRsp = {    
        //搜索关键字
        keyword:string    
        
        limit:number    
        //【市】医保甲类总数
        medicalFeeGradeACount:number    
        //【市】医保乙类总数
        medicalFeeGradeBCount:number    
        //【市】医保丙类总数
        medicalFeeGradeCCount:number    
        //【市】医保等级总数
        medicalFeeGradeCount:number    
        //分页参数
        offset:number    
        //商品列表，相比之前老的接口进行了部分精简
        rows?:Array<DiagnosisAndTreatGoodsView>    
        //社保已失效数量
        shebaoExpiredCount:number    
        //社保即将失效数量
        shebaoGoingExpireCount:number    
        //社保即将超限价数量
        shebaoGoingOverPriceCount:number    
        //社保已失效数量 详细【国家】
        shebaoNationalExpiredCount:number    
        //社保即将失效数量 详细【国家】
        shebaoNationalGoingExpireCount:number    
        //社保即将超限价数量 详细【国家】
        shebaoNationalGoingOverPriceCount:number    
        //【国家】社保对码数量
        shebaoNationalMatchCount:number    
        //【国家】社保未对码数量
        shebaoNationalNotMatchCount:number    
        //【国家】禁止社保支付的数量
        shebaoNationalNotPermitCount:number    
        //社保已超限价数量 详细【国家】
        shebaoNationalOverPriceCount:number    
        //社保已超限价数量
        shebaoOverPriceCount:number    
        //搜索子类型
        subType:string    
        
        total:number    
        //搜索类型
        type:string    
    }
}
