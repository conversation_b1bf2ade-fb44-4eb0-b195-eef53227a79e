declare namespace  AbcAPI {
    
    type ApprovalTicketUpgradeClinicView = {    
        //当前用户允许的操作 [cancel|allow|deny]
        allowActions?:Array<any>    
        
        approvalId:number    
        
        created:string    
        
        createdBy:number    
        
        createdByName:string    
        
        currentTicketFlow?:ApprovalTicketFlowView    
        
        id:string    
        //支付记录附件
        payAttachments?:Array<any>    
        //支付方式
        payMode:number    
        
        payModeName:string    
        //支付状态
        payStatus:number    
        
        requestContent?:UpgradeClinicContentRequest    
        
        resultContent?:UpgradeClinicContentResult    
        //当前单据状态 11(支付中);12(审核中);13(执行中);90(已完成);99(已取消);
        status:number    
        
        statusName:string    
        
        title:string    
    }
}
