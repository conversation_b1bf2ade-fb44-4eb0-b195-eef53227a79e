/*
 * <AUTHOR> 
 * @DateTime 2024-06-20 16:52:43 
 */
import { reactive } from 'vue';

type pageParamsType = {
    page: number, // 当前页, 1 开始
    pageSize: number, // 每页条数
    total: number, // 总条数
}

const createPageModel = () => {
    const params = reactive<pageParamsType>({
        page: 1,
        pageSize: 10,
        total: 0,
    });

    const setPage = (page:number) => {
        params.page = page;
    };

    const addPage = () => {
        params.page += 1;
    };

    const setTotal = (total:number) => {
        params.total = total;
    };

    const initParams = () => {
        params.page = 1;
        params.total = 0;
    };

    const createFetchParams = () => ({
        offset: (params.page - 1) * params.pageSize,
        limit: params.pageSize,
    });

    const createSliceParams = () => ({
        sIndex: (params.page - 1) * params.pageSize,
        eIndex: params.page * params.pageSize,
    });

    return {
        params,
        setPage,
        addPage,
        setTotal,
        initParams,
        createFetchParams,
        createSliceParams,
    };
};

export default createPageModel;