import BaseAPI from './base-api';

/**
* 
*/
export class DataTransferApi extends BaseAPI {
    /**
    * 获取下载模版列表
    * @param {number} offset -     
    * @param {number} limit -     
    */
    static getApiLowCodeDataTransferTemplateList(
        offset:number,
        limit:number,
    ) {
        return this.get<AbcAPI.DataTransferImportTemplate>('/api/low-code/data-transfer/template/list', {
            params: {
                offset,
                limit,
            },
        });
    }
    
    /**
    * 获取竞品列表
    * @param {number} isSaas -     
    * @param {number} offset -     
    * @param {number} limit -     
    */
    static getApiLowCodeDataTransferCompetitiveProductList(
        isSaas:number,
        offset?:number,
        limit?:number,
    ) {
        return this.get<AbcAPI.DataTransferImportTemplate>('/api/low-code/data-transfer/competitive-product/list', {
            params: {
                isSaas,
                offset,
                limit,
            },
        });
    }
    
    /**
    * 获取数据迁移支持类型列表
    */
    static getApiLowCodeDataTransferDataTransferTypeList(
    ) {
        return this.get<AbcAPI.DataTransferDataTypes>('/api/low-code/data-transfer/data-transfer/type/list');
    }
    
    /**
    * 获取数据清理支持类型列表
    */
    static getApiLowCodeDataTransferDataClearingTypeList(
    ) {
        return this.get<AbcAPI.DataClearingMatrix>('/api/low-code/data-transfer/data-clearing/type/list');
    }
    
    /**
    * 获取数据迁移单列表
    * @param {number} offset -     
    * @param {number} limit -     
    * @param {string} clinicId -     
    * @param {any} beginDate -     
    * @param {any} endDate -     
    * @param {number} status -     
    * @param {string} executor -     
    */
    static getApiLowCodeDataTransferRecordList(
        offset:number,
        limit:number,
        clinicId?:string,
        beginDate?:any,
        endDate?:any,
        status?:number,
        executor?:string,
    ) {
        return this.get<AbcAPI.DataTransferRecord>('/api/low-code/data-transfer/record/list', {
            params: {
                offset,
                limit,
                clinicId,
                beginDate,
                endDate,
                status,
                executor,
            },
        });
    }
    
    /**
    * 获取数据迁移单详情
    * @param {number} type -     
    * @param {string} id -     
    */
    static getApiLowCodeDataTransferRecordDetailById(
        type:number,
        id:string,
    ) {
        return this.get<AbcAPI.DataTransferRecord>(`/api/low-code/data-transfer/record/detail/${id}`, {
            params: {
                type,
            },
        });
    }
    
    /**
    * 表格导入数据迁移
    * @param {AbcAPI.CreateTableImportRecordDto} createTableImportRecordDto -     
    */
    static postApiLowCodeDataTransferTableImportCreate(
        createTableImportRecordDto:AbcAPI.CreateTableImportRecordDto,
    ) {
        return this.post<AbcAPI.DataTransferTableData>(
            '/api/low-code/data-transfer/table-import/create',
            createTableImportRecordDto,
        );
    }
    
    /**
    * 表格导入数据迁移编辑
    * @param {AbcAPI.CreateTableImportRecordDto} createTableImportRecordDto -     
    * @param {string} id -     
    */
    static putApiLowCodeDataTransferTableImportUpdateById(
        createTableImportRecordDto:AbcAPI.CreateTableImportRecordDto,
        id:string,
    ) {
        return this.put<AbcAPI.DataTransferTableData>(
        `/api/low-code/data-transfer/table-import/update/${id}`,
        createTableImportRecordDto,
        );
    }
    
    /**
    * 竞品导入数据迁移
    * @param {AbcAPI.CreateCompetitiveProductDto} createCompetitiveProductDto -     
    */
    static postApiLowCodeDataTransferCompetitiveProductImportCreate(
        createCompetitiveProductDto:AbcAPI.CreateCompetitiveProductDto,
    ) {
        return this.post<AbcAPI.DataTransferCompetitorSupportData>(
            '/api/low-code/data-transfer/competitive-product-import/create',
            createCompetitiveProductDto,
        );
    }
    
    /**
    * 竞品导入数据迁移编辑
    * @param {AbcAPI.CreateCompetitiveProductDto} createCompetitiveProductDto -     
    * @param {string} id -     
    */
    static putApiLowCodeDataTransferCompetitiveProductImportUpdateById(
        createCompetitiveProductDto:AbcAPI.CreateCompetitiveProductDto,
        id:string,
    ) {
        return this.put<AbcAPI.DataTransferCompetitorSupportData>(
        `/api/low-code/data-transfer/competitive-product-import/update/${id}`,
        createCompetitiveProductDto,
        );
    }
    
    /**
    * ABC门店导入数据迁移
    * @param {AbcAPI.CreateAbcClinicImportRecordDto} createAbcClinicImportRecordDto -     
    */
    static postApiLowCodeDataTransferAbcClinicImportCreate(
        createAbcClinicImportRecordDto:AbcAPI.CreateAbcClinicImportRecordDto,
    ) {
        return this.post<AbcAPI.DataTransferClinicData>(
            '/api/low-code/data-transfer/abc-clinic-import/create',
            createAbcClinicImportRecordDto,
        );
    }
    
    /**
    * ABC门店导入数据迁移编辑
    * @param {AbcAPI.CreateAbcClinicImportRecordDto} createAbcClinicImportRecordDto -     
    * @param {string} id -     
    */
    static putApiLowCodeDataTransferAbcClinicImportUpdateById(
        createAbcClinicImportRecordDto:AbcAPI.CreateAbcClinicImportRecordDto,
        id:string,
    ) {
        return this.put<AbcAPI.DataTransferClinicData>(
        `/api/low-code/data-transfer/abc-clinic-import/update/${id}`,
        createAbcClinicImportRecordDto,
        );
    }
    
    /**
    * 新建表格数据清理
    * @param {AbcAPI.CreateDataClearingRecordDto} createDataClearingRecordDto -     
    */
    static postApiLowCodeDataTransferDataClearingCreate(
        createDataClearingRecordDto:AbcAPI.CreateDataClearingRecordDto,
    ) {
        return this.post<AbcAPI.DataClearingData>(
            '/api/low-code/data-transfer/data-clearing/create',
            createDataClearingRecordDto,
        );
    }
    
    /**
    * 表格数据清理编辑
    * @param {AbcAPI.CreateDataClearingRecordDto} createDataClearingRecordDto -     
    * @param {string} id -     
    */
    static putApiLowCodeDataTransferDataClearingUpdateById(
        createDataClearingRecordDto:AbcAPI.CreateDataClearingRecordDto,
        id:string,
    ) {
        return this.put<AbcAPI.DataTransferTableData>(
        `/api/low-code/data-transfer/data-clearing/update/${id}`,
        createDataClearingRecordDto,
        );
    }
    
    /**
    * 删除数据迁移
    * @param {AbcAPI.DeletedDataTransferDto} deletedDataTransferDto -     
    * @param {string} id -     
    */
    static deleteApiLowCodeDataTransferDeletedById(
        deletedDataTransferDto:AbcAPI.DeletedDataTransferDto,
        id:string,
    ) {
        return this.del<AbcAPI.DataTransferRecord>(
        `/api/low-code/data-transfer/deleted/${id}`,
        { data: deletedDataTransferDto },
        );
    }
    
    /**
    * tapd状态修改
    */
    static postApiLowCodeDataTransferUpdateTapdStatus(
    ) {
        return this.post<AbcAPI.DataTransferRecord>('/api/low-code/data-transfer/update/tapd/status');
    }
}