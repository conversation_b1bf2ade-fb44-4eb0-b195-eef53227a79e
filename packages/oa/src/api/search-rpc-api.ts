import BaseAPI from './base-api';

/**
* 搜索接RPC口
*/
export class SearchRpcAPI extends BaseAPI {
    /**
    * doSearchHelperQuestions
    * @param {string} keyword - keyword    
    * @param {string} categoryId - categoryId    
    * @param {number} limit - limit    
    * @param {number} offset - offset    
    * @param {number} type - type    
    */
    static doSearchHelperQuestionsUsingGET(
        keyword:string,
        categoryId?:string,
        limit?:number,
        offset?:number,
        type?:number,
    ) {
        return this.get<AbcAPI.SearchResultListRspHelperQuestionsSearchResult>('/rpc/management/search/helper-questions', {
            params: {
                categoryId,
                keyword,
                limit,
                offset,
                type,
            },
        });
    }
}