/**
 * 基于 amfe-flexible 修改，同时兼容 PC 和 H5
 */
(function flexible(window, document) {
    let docEl = document.documentElement;
    let dpr = window.devicePixelRatio || 1;

    // adjust body font size
    function setBodyFontSize() {
        if (document.body) {
            document.body.style.fontSize = (12 * dpr) + 'px';
        } else {
            document.addEventListener('DOMContentLoaded', setBodyFontSize);
        }
    }
    setBodyFontSize();
    const baseSize = 16;

    function setRemUnit() {
        let rem;
        if (docEl.clientWidth > 767) {
            // PC
            const scale = document.documentElement.clientWidth / 480;
            // 设置页面根节点字体大小, 字体大小最小为12
            docEl.style.fontSize = ((baseSize * Math.min(scale, 2.5)) > 12 ? (baseSize * Math.min(scale, 2.5)) : 12) + 'px';
        } else {
            // H5: set 1rem = viewWidth / 10
            rem = docEl.clientWidth / 10;
            docEl.style.fontSize = rem + 'px';
        }
    }

    setRemUnit();

    // reset rem unit on page resize
    window.addEventListener('resize', setRemUnit);
    window.addEventListener('pageshow', (e) => {
        if (e.persisted) {
            setRemUnit();
        }
    });

    // detect 0.5px supports
    if (dpr >= 2) {
        let fakeBody = document.createElement('body');
        let testElement = document.createElement('div');
        testElement.style.border = '.5px solid transparent';
        fakeBody.appendChild(testElement);
        docEl.appendChild(fakeBody);
        if (testElement.offsetHeight === 1) {
            docEl.classList.add('hairlines');
        }
        docEl.removeChild(fakeBody);
    }
}(window, document));
