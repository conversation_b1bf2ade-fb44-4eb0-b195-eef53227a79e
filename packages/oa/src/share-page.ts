import { createApp } from 'vue';
import router from '@/router/share-page';
import { createPinia } from 'pinia';
import './utils/flexible.ts';
import '@/style/index.scss';
import vant from '@/vendor/vant';
import { ElSelect, ElOption, ElSelectV2 } from 'element-plus';
import 'element-plus/dist/index.css';
import App from './SharePage.vue';

const app = createApp(App);

app
                .use(router)
                .use(createPinia())
                .use(vant)
                .use(ElSelect)
                .use(ElSelectV2)
                .use(ElOption)
                .mount('#app');

console.log('构建环境:', import.meta.env.VITE_APP_BUILD_ENV);
console.log('时间:', import.meta.env.VITE_APP_BUILD_TIME);
