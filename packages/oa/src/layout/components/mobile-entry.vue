<script lang="ts" setup>
import { useMenuStore } from '@/store/menu';
import { computed } from 'vue';
import { RouteRecordRaw, useRoute } from 'vue-router';
import { useThemeConfigStore } from '@/store/theme-config';

const route = useRoute();
const visibleTabBar = computed(() => !route.meta.hideTabBar);
const themeConfig = useThemeConfigStore();

const menuStore = useMenuStore();

const layoutPageClass = computed(() => {
    const classes = ['layout-tabbar-page-wrapper'];
    if (themeConfig.isMobile) return classes;
    if (themeConfig.isCollapse) {
        classes.push('layout-tabbar-page__sidebar--close');
    } else {
        classes.push('layout-tabbar-page__sidebar--open');
    }
    return classes;
});

// 查找最近的 tabbar 入口路由
function findLatestTabEntryRoute(menus: Array<RouteRecordRaw>): RouteRecordRaw | null {
    for (let menu of menus) {
        // 当前路由是入口页且当前访问的路由是(路由本身||子路由)
        if (menu.meta?.isEntry && (menu.name === route.name || menu.children?.some((subRoute) => subRoute.name === route.name))) {
            return menu;
        }
        if (menu.children) {
            let m = findLatestTabEntryRoute(menu.children);
            if (m) {
                return m;
            }
        }
    }
    return null;
}
const subMenus = computed(() => (
    findLatestTabEntryRoute(menuStore.menus)?.children ?? []
).filter(menu => !menu.meta?.hidden));
</script>

<template>
    <div :class="layoutPageClass">
        <div class="layout-tabbar-page__content">
            <router-view></router-view>
        </div>
        <van-tabbar v-if="visibleTabBar" route class="layout-tabbar-page__tabbar">
            <template v-for="tabItem in subMenus">
                <van-tabbar-item replace :to="{name: tabItem.name}">{{ tabItem.meta?.name || tabItem.name }}</van-tabbar-item>
            </template>
        </van-tabbar>
    </div>
</template>
