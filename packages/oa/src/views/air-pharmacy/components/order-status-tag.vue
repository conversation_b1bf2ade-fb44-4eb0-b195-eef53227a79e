<script setup lang="ts">
import { OrderStatus } from '@/views/order/model/model';
import { computed, PropType } from 'vue';

const props = defineProps({
    status: {
        type: Number as PropType<OrderStatus>,
        required: true,
    },
    statusName: {
        type: String,
    },
});

const colorMap = {
    [OrderStatus.WAITING_PAY]: 'var(--oa-success-color)',
    [OrderStatus.WAITING_AUDIT]: 'var(--oa-primary-color)',
    [OrderStatus.DONE]: 'var(--oa-text-color-3)',
    [OrderStatus.CANCELED]: 'var(--oa-text-color-3)',
    [OrderStatus.EXECUTING]: 'var(--oa-primary-color)',
};

const computedTagColor = computed(() => colorMap[props.status] || '');
</script>
<template>
    <van-tag plain :color="computedTagColor">{{ statusName }}</van-tag>
</template>
