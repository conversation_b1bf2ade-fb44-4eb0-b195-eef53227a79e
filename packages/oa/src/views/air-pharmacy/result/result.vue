<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { isOrderNeedPay } from '@/views/air-pharmacy/model/adapter';

const props = defineProps({
    id: {
        type: String,
        required: true,
    },
});

const router = useRouter();
const route = useRoute();

// @ts-ignore
const needPay = computed(() => isOrderNeedPay(+route.query?.orderType));

function toDetail() {
    let routeName = needPay.value ? '@air-pharmacy/pay' : '@air-pharmacy/detail';
    router.replace({
        name: routeName,
        params: {
            id: props.id,
        },
        query: {
            orderType: route.query?.orderType,
        },
    });
}
</script>
<template>
    <div class="order-result-wrapper">
        <img class="order-result__icon" src="@/assets/success-green.png" />
        <div class="order-result__title">创建成功</div>
        <van-button
            class="order-result__btn"
            plain
            type="success"
            @click="toDetail"
        >
            {{ needPay ? '去收款' : '查看详情' }}
        </van-button>
    </div>
</template>

<style lang="scss">
    .order-result-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;

        .order-result__icon {
            width: 80px;
            height: 80px;
            margin-top: 24px;
        }

        .order-result__title {
            color: var(--oa-text-color);
            font-size: var(--oa-font-size-18);
            margin-bottom: 24px;
        }
    }
</style>
