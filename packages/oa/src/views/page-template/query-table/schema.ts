import { ReportActiveClinicsFunction } from '@/vendor/cloud-function';

export const querySchema = {
    type: 'object',
    component: 'QueryTable',
    actions: {
        search: {
            type: 'query',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '查询',
                type: 'primary',
            },
        },
    },
    properties: {
        date: {
            label: '日期',
            type: 'string',
            component: 'DatePicker',
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
            },
        },
        keyword: {
            label: '关键字',
            type: 'string',
            component: 'Input',
            componentProps: {
                placeholder: '请输入',
            },
        },
        // clinicId: {
        //     label: '门店',
        //     type: 'string',
        //     component: 'OrganPicker',
        // },
        // employeeId: {
        //     label: '人员',
        //     type: 'string',
        //     // watch: {
        //     //     '$form.clinicId': {
        //     //         handler: 'reset',
        //     //     },
        //     // },
        //     component: 'EmployeePicker',
        // },
        clinicIdEmployeeId: {
            label: '门店-人员',
            type: 'object',
            component: 'OrganEmployeePicker',
            flatten: true,
            properties: {
                clinicId: {
                    type: 'string',
                },
                employeeId: {
                    type: 'string',
                },
                department: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'string',
                        },
                    },
                },
            },
        },
    },
};

export const tableSchema = {
    // 数据源
    dataSource: {
        type: 'cloud-function',
        func: ReportActiveClinicsFunction.NAME,
        params: {
            date: {
                type: 'date',
            },
        },
        result: {
            type: 'list',
        },
    },
    // 表格字段定义
    columns: [
        { prop: 'name', name: '门店' },
        { prop: 'count', name: '收费次数', align: 'right' },
    ],
    // 是否在查询条件就绪后立即请求数据
    requestWhenQueryPrepared: true,
    // 是否在查询条件改变后请求数据
    requestWhenQueryChanged: true,
    // 是否显示总数
    visibleTotal: true,
};
