import { reactive, ref } from 'vue';
import { GoodsExtendSpecType } from './constant';

type FilterParams = {
    connectStatus: number | undefined,
    keyword: string,
}

export const useModel = () => {
    const loading = ref(false);
    
    const deviceData = ref<any[]>([]);

    const filterParams = reactive<FilterParams>({
        connectStatus: undefined,
        keyword: '',
    });

    const paginationProps = reactive({
        page: 1,
        pageSize: 10,
        total: 0,
    });

    const dialogVisible = ref(false);

    const editDeviceId = ref('');

    function setLoading(val:boolean) {
        loading.value = val;
    }

    function setDeviceData(data:any[]) {
        deviceData.value = data.map(item => ({
            ...item,
            isEye: +item.goodsExtendSpec === GoodsExtendSpecType.eye,
        }));
    }

    function setDialogVisible(val:boolean) {
        dialogVisible.value = val;
    }

    function setPagination(params:{
        page?: number,
        total?: number,
    }) {
        params.page && (paginationProps.page = params.page);
        params.total && (paginationProps.total = params.total);
    }

    return {
        deviceData,
        filterParams,
        paginationProps,
        dialogVisible,
        loading,
        editDeviceId,
        setDeviceData,
        setDialogVisible,
        setPagination,
        setLoading,
    };
};

export type Model = ReturnType<typeof useModel>;