<script setup lang="ts">
const props = defineProps({
    timeline: {
        type: Object,
        required: true,
    },
});

/**
 * 获取链接后缀
 * @param url 链接
 */
const getVoiceType = (url: string) => {
    if (!url) { return ''; }
    // 去除查询参数和 hash
    const urlWithoutQuery = url.split('?')[0].split('#')[0];
    // 提取文件扩展名
    const match = urlWithoutQuery.match(/\.([^.]+)$/);
    return match ? match[1].toLowerCase() : '';
};

/**
 * 图片点击事件
 */
const handleImageClick = () => {
    const imageView = document.querySelector('.el-image-viewer__wrapper__hidden.display-none');
    if (imageView) {
        imageView.className = 'el-image-viewer__wrapper';
    }
};
</script>
<template>
    <span v-if="timeline.msgType === 'text'">{{ timeline.msgText?.content || '' }}</span>
    <el-image
        v-else-if="timeline.msgType === 'image'"
        :id="timeline.id"
        style="max-height: 200px; max-width: 200px; min-height: 26px;"
        :src="timeline.msgImage?.ossUrl"
        :preview-src-list="[timeline.msgImage?.ossUrl]"
        :initial-index="4"
        :close-on-press-escape="false"
        hide-on-click-modal
        fit="contain"
        alt="图片"
        @click="handleImageClick"
    >
        <template #placeholder>
            <div class="image-slot">
                <el-icon color="#ccc" size="24px"><Picture /></el-icon>
            </div>
        </template>
    </el-image>
    <div v-else-if="timeline.msgType === 'voice'">
        <audio v-if="getVoiceType(timeline?.msgVoice?.ossUrl) !== 'amr' && timeline?.msgVoice?.ossUrl" controls :src="timeline?.msgVoice?.ossUrl"></audio>
        <span v-else-if="getVoiceType(timeline?.msgVoice?.ossUrl) !== 'amr'" class="conversation-voice-error">
            <el-icon><Mute /></el-icon>
            <span style="padding-left: 12px;">音频文件丢失...</span>
        </span>
        <div v-else>
            您的浏览器不支持录音音频格式，<a :href="timeline?.msgVoice?.ossUrl || ''">点击下载音频;</a>
            <p v-if="timeline.msgText?.content">语音转文字内容：{{ timeline.msgText?.content }}</p>
        </div>
    </div>
    <video
        v-else-if="timeline.msgType === 'video'"
        controls
        style="max-height: 400px;"
        :src="timeline?.msgVideo?.ossUrl"
    ></video>
    <p v-else-if="timeline.msgType === 'revoke'">
        <span>你撤回了一条消息...</span>
    </p>
    <a v-else-if="timeline.msgType === 'file'" :href="timeline?.msgFile?.ossUrl">点击获取相关文件</a>
    <a v-else-if="timeline.msgType === 'link'" target="_blank" :href="timeline.msgLink?.linkUrl">{{ timeline.msgLink?.title || '链接地址' }}</a>
    <div v-else-if="timeline.msgType === 'location'">
        <p>地名：{{ timeline.msgLocation?.name || '' }}</p>
        <p>地址：{{ timeline.msgLocation?.address || '' }}</p>
        <p>{{ `经度：${timeline.msgLocation?.longitude || ''}   纬度：${ timeline.msgLocation?.latitude ||''}` }} </p>
    </div>
    <div v-else-if="timeline.msgType === 'card'">
        名片：
        <p>{{ timeline.msgCard?.corpname || '' }}</p>
        <span>{{ timeline.msgCard?.userid || '' }}</span>
    </div>
    <div v-else-if="timeline.msgType === 'msgmenu'">
        <el-card>
            <template #header>
                <div class="card-header">
                    <span>{{ timeline.msgMenu?.head_content || '' }}</span>
                </div>
            </template>
            <template v-if="timeline.msgMenu?.list">
                <p v-for="msg in timeline.msgMenu.list" :key="msg.type">
                    <span v-if="msg.type === 'click'">{{ msg.click?.content || '' }}</span>
                    <a v-else-if="msg.type === 'view'" target="_blank" :href="msg.view?.url">{{ msg.view?.content || '' }}</a>
                    <a
                        v-else-if="msg.type === 'miniprogram'"
                        target="_blank"
                        :href="msg.miniprogram?.pagepath"
                    >{{ msg.miniprogram?.content || '' }}</a>
                </p>
            </template>
            <p v-if="timeline.msgMenu?.tail_content">{{ timeline.msgMenu.tail_content }}</p>
        </el-card>
    </div>
    <div v-else-if="timeline.msgType === 'voiptext'" class="conversation-voiptext">
        <el-icon v-if="timeline.msgVoiptext?.invitetype % 2 === 0"><PhoneFilled /></el-icon>
        <el-icon v-else><VideoCamera /></el-icon>
        <span style="padding-left: 12px;">本次通话：{{ timeline.msgVoiptext?.callduration || '0' }}s</span>
    </div>
    <span v-else>其他消息类型暂未支持，请期待后续更新！</span>
</template>
<style lang="scss">
.conversation-voiptext,
.conversation-voice-error {
    width: 200px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px;
    border-radius: 4px;
    background-color: #f5f7fa;
}
</style>
