<script lang="ts" setup="">
import { computed, nextTick, onMounted, PropType, ref } from 'vue';
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.prod.css';

// @ts-expect-error
import BlotFormatter from 'quill-blot-formatter/dist/BlotFormatter.js';
import CmsOaMaintainPushVo = AbcAPI.CmsOaMaintainPushVo;
import { clone } from '@/utils/utils';
import { CmsAPI } from '@/api/cms-api';
import { useService } from '../service';
import { ElMessage } from 'element-plus';

const $emit = defineEmits(['update:modelValue', 'refresh']);
const props = defineProps({
    value: {
        type: Boolean,
        default: false,
    },
    message: {
        type: Object as PropType<CmsOaMaintainPushVo>,
        required: true,
    },
});
const dialogVisible = computed({
    get() {
        return props.value;
    },
    set(val:boolean) {
        $emit('update:modelValue', val);
    },
});
const postData = ref(<CmsOaMaintainPushVo>{
    // 链接
    articleUrl: '',
    articleTitle: '',
    // 推送类型 101:发布预告 102:更新公告
    cmsPushType: 0,
    // 内容
    content: '',
    // OA通知ID
    oaNotifyId: '',
    // 推送ID
    pushId: '',
    // 标题
    title: '',
});

const EditorOptions = ref({
    modules: [
        {
            name: 'blotFormatter',
            module: BlotFormatter,
            options: {
                overlay: {
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ],
});

const contentRef = ref();
onMounted(async () => {
    postData.value = clone(props.message);
    await nextTick();
    contentRef.value.setHTML(postData.value.content);
});

const querySearch = async (val: string, cb: Function) => {
    try {
        const res = await CmsAPI.queryCmsLogPageUsingGET(undefined, val, 1000, 0, '', '', 1);
        const list = res?.rows || [];
        cb(list);
    } catch (e) {
        console.warn('文章列表错误', e);
    }
};

const selectHandler = (item: any) => {
    postData.value.articleUrl = item.link;
    postData.value.articleTitle = item.title;
};

const service = useService();
const handleUpdate = async () => {
    const pushId = postData.value.pushId;
    try {
        await service.updatePushItem(pushId, postData.value);
        ElMessage.success({
            message: '修改成功',
        });
        $emit('refresh');
        dialogVisible.value = false;
    } catch (e: any) {
        ElMessage.error({
            message: e.message,
        });
    }
};
</script>

<template>
    <el-dialog v-model="dialogVisible" title="编辑推送消息" custom-class="maintenance-dialog-wrapper">
        <el-form>
            <el-form-item label="标题">
                <el-input v-model="postData.title"></el-input>
            </el-form-item>
            <el-form-item label="内容">
                <div>
                    <quill-editor
                        ref="contentRef"
                        v-model:content="postData.content"
                        :content="postData.content"
                        content-type="html"
                        toolbar="full"
                        theme="snow"
                        :modules="EditorOptions.modules"
                        :options="EditorOptions"
                    >
                    </quill-editor>
                </div>
            </el-form-item>

            <el-form-item label="链接">
                <el-autocomplete
                    v-model="postData.articleTitle"
                    :fetch-suggestions="querySearch"
                    :trigger-on-focus="false"
                    @select="selectHandler"
                >
                    <template #default="{item}">
                        <div>{{ item.title }}</div>
                    </template>
                </el-autocomplete>
                <span style="margin-left: 8px;">{{ postData.articleUrl }}</span>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleUpdate">
                    保存
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="scss">

.maintenance-dialog-wrapper {
    .ql-container {
        height: 300px;
        max-height: 300px;
        overflow: scroll;
    }
}

</style>