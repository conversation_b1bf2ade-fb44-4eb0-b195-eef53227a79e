import { ElMessage, ElMessageBox } from 'element-plus';
import { useService } from '../../service';
import { Model } from './model';
import { getHtmlUrl, getConfigJsonUrl } from '@/views/devops/get-html';
import { DevopsNoticeClassNameEnum, DevopsNoticeStatus, DevopsNoticeType } from '@/views/devops/model/constants';
import { CmsAPI } from '@/api/cms-api';

export const useController = (model: Model) => {
    const service = useService(DevopsNoticeType.MaintenanceNotice);

    async function initData() {
        try {
            const { rows } = await service.getNoticeList();
            if (rows?.length) {
                model.postData.value = {
                    ...rows[0],
                    regionId: rows[0].regionId || '',
                };
                model.contentRef.value.setHTML(model.postData.value.content);
                model.preContentRef.value.setHTML(model.postData.value.preContent);
            } else {
                model.contentRef.value.setHTML('');
                model.preContentRef.value.setHTML('');
            }
        } catch (e) {
            console.log(e);
            ElMessage.error('维护公告数据失败');
        }
        setIsEdit(false);
    }

    function setIsEdit(isEdit: boolean) {
        model.isEdit.value = isEdit;
        model.contentRef.value.getQuill().enable(isEdit);
        model.preContentRef.value.getQuill().enable(isEdit);
    }

    function handleEditClick() {
        setIsEdit(true);
    }

    function handleCancelEditClick() {
        setIsEdit(false);
    }

    async function handleSaveClick() {
        try {
            model.saveBtnLoading.value = true;
            model.postData.value.applyAll = 1;
            if (model.postData.value.id) {
                await service.upsertConfig(model.postData.value.id, model.postData.value);
            } else {
                await service.createConfig(model.postData.value);
            }
            ElMessage.success('提交成功');
            model.saveBtnLoading.value = false;
            initData();
        } catch (e) {
            model.saveBtnLoading.value = false;
        }
    }
    async function handleChangeNormal() {
        try {
            await ElMessageBox.prompt(
                '请输入：我已确定更新后服务正常运行，并为造成的影响负责',
                '确定修改停机配置',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    showInput: true,
                    inputValidator: (str) => str === '我已确定更新后服务正常运行，并为造成的影响负责',
                    inputErrorMessage: '请输入：我已确定更新后服务正常运行，并为造成的影响负责',
                },
            );
            model.normalBtnLoading.value = true;
            try {
                model.postData.value.redirectUrl = await getHtmlUrl('', '', '', true);
                model.postData.value.configUrl = await getConfigJsonUrl({
                    ...model.postData.value,
                    status: DevopsNoticeStatus.Normal,
                });
                await service.upsertConfig(model.postData.value.id, model.postData.value);
                await service.updateStatus(model.postData.value.id, DevopsNoticeStatus.Normal);
                ElMessage.success('提交成功');
                initData();
            } catch (e: any) {
                ElMessage.error('修改维护失败: ' + e.message);
                return;
            } finally {
                model.normalBtnLoading.value = false;
            }
        } catch (e) {

        }
    }

    async function handleRecovery() {
        try {
            await ElMessageBox.prompt(
                '请输入：我已确定发布完成恢复服务，并为造成的影响负责',
                '确定修改停机配置',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    showInput: true,
                    inputValidator: (str) => str === '我已确定发布完成恢复服务，并为造成的影响负责',
                    inputErrorMessage: '请输入：我已确定发布完成恢复服务，并为造成的影响负责',
                },
            );
            model.recoveryBtnLoading.value = true;
            try {
                model.recoveryBtnLoading.value = true;
                model.postData.value.redirectUrl = await getHtmlUrl(
                    `${model.postData.value.title || ''}全量更新发布已经完成，ABC向更好的产品迈出了又一小步。`,
                    '', DevopsNoticeClassNameEnum.MaintenanceSuccessNotice,
                );
                model.postData.value.configUrl = await getConfigJsonUrl({
                    ...model.postData.value,
                    status: DevopsNoticeStatus.Recovery,
                });
                await service.upsertConfig(model.postData.value.id, {
                    ...model.postData.value,
                });
                await service.updateStatus(model.postData.value.id, DevopsNoticeStatus.Recovery);
                initData();
                ElMessage.success('提交成功');
            } catch (e: any) {
                ElMessage.error('修改维护失败: ' + e.message);
                return;
            } finally {
                model.recoveryBtnLoading.value = false;
            }
            initData();
        } catch (e) {
            model.recoveryBtnLoading.value = false;
        }
    }
    async function handleStopService() {
        try {
            if (!model.postData.value.content) {
                ElMessage.warning('请填写公告内容');
                return;
            }
            if (!model.postData.value.preContent) {
                ElMessage.warning('请填写更新预告内容');
                return;
            }

            if (model.isEdit.value) {
                ElMessage.warning('请先保存公告内容');
                return;
            }

            model.stopBtnLoading.value = true;
            await ElMessageBox.prompt(
                '请输入：我已确定修改停机配置，并为造成的影响负责',
                '确定修改停机配置',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    showInput: true,
                    inputValidator: (str) => str === '我已确定修改停机配置，并为造成的影响负责',
                    inputErrorMessage: '请输入：我已确定修改停机配置，并为造成的影响负责',
                },
            );
            try {
                model.postData.value.redirectUrl = await getHtmlUrl(
                    `${model.postData.value.title || ''}全量更新发布正在进行中，升级过程会持续数小时。期待顺利发布后与您再次见面！`,
                    '系统维护中',
                    DevopsNoticeClassNameEnum.MaintenanceNotice,
                );
                model.postData.value.configUrl = await getConfigJsonUrl({
                    ...model.postData.value,
                    status: DevopsNoticeStatus.Shutdown,
                });
                await service.upsertConfig(model.postData.value.id, {
                    ...model.postData.value,
                });
                await service.updateStatus(model.postData.value.id, DevopsNoticeStatus.Shutdown);
                setIsEdit(false);
            } catch (e: any) {
                ElMessage.error('修改停机设置失败: ' + e.message);
                return;
            } finally {
                model.stopBtnLoading.value = false;
            }
            initData();
        } catch (e) {
            model.stopBtnLoading.value = false;
        }
    }
    async function handlePreContentPublish() {
        try {
            if (model.isEdit.value) {
                ElMessage.warning('请先保存公告内容');
                return;
            }

            model.preBtnLoading.value = true;
            await ElMessageBox.prompt(
                '请输入：我已确定发布预告，并为造成的影响负责',
                '确定是否推送',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    showInput: true,
                    inputValidator: (str) => str === '我已确定发布预告，并为造成的影响负责',
                    inputErrorMessage: '请输入：我已确定发布预告，并为造成的影响负责',
                },
            );
            try {
                await service.publishPreview(model.postData.value.id);
            } catch (e: any) {
                ElMessage.error('推送失败: ' + e.message);
                return;
            } finally {
                model.preBtnLoading.value = false;
            }
            initData();
        } catch (e) {
            model.preBtnLoading.value = false;
        }
    }
    async function handleContentPublish() {
        try {
            if (model.isEdit.value) {
                ElMessage.warning('请先保存公告内容');
                return;
            }

            model.pushContentBtnLoading.value = true;
            await ElMessageBox.prompt(
                '请输入：我已确定发布完成，推送更新内容通知，并为造成的影响负责',
                '确定是否推送',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    showInput: true,
                    inputValidator: (str) => str === '我已确定发布完成，推送更新内容通知，并为造成的影响负责',
                    inputErrorMessage: '请输入：我已确定发布完成，推送更新内容通知，并为造成的影响负责',
                },
            );
            try {
                await service.publishContent(model.postData.value.id);
            } catch (e: any) {
                ElMessage.error('推送失败: ' + e.message);
                return;
            } finally {
                model.pushContentBtnLoading.value = false;
            }
            initData();
        } catch (e) {
            model.pushContentBtnLoading.value = false;
        }
    }

    async function querySearch(val: string, cb: Function) {
        try {
            const res = await CmsAPI.queryCmsLogPageUsingGET(undefined, val, 1000, 0, '', '', 1);
            const list = res?.rows || [];
            cb(list);
        } catch (e) {
            console.warn('文章列表错误', e);
        }
    }
    function selectHandler(item: any) {
        model.postData.value.articleUrl = item.link;
        model.postData.value.articleTitle = item.title;
    }

    return {
        initData,
        setIsEdit,
        handleEditClick,
        handleSaveClick,
        handleCancelEditClick,
        handleChangeNormal,
        handleRecovery,
        handleStopService,
        handlePreContentPublish,
        handleContentPublish,
        querySearch,
        selectHandler,
    };
};
