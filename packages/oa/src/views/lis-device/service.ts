import { Model } from './model';
import { GoodsExamOaAPI } from '@/api/goods-exam-oa-api';
import { BusinessTypeEnum } from './constant';

export const useService = (model:Model) => {
    async function getDeviceData() {
        try {
            const res = await GoodsExamOaAPI.getExamAssayDeviceModelListUsingGET(
                model.paginationProps.pageSize,
                model.paginationProps.pageSize * (model.paginationProps.page - 1),
                model.filterParams.connectStatus,
                BusinessTypeEnum.EXAMINATION,
                model.filterParams.keyword,
            );
            const data = res.rows?.filter(item => item.innerFlag !== 1) || [];
            model.setDeviceData(data);
            model.setPagination({
                total: res.total || 0,
            });
        } catch (error) {
            console.log(error);
        }
    }

    return {
        getDeviceData,
    };
};

export type Service = ReturnType<typeof useService>;