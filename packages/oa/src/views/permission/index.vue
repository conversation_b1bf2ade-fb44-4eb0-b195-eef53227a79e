<script setup lang="ts">
import KvCard from '@/components/kv-card.vue';
import { useUserStore } from '@/store/user';
import { reactive } from 'vue';

const userStore = useUserStore();
const columns = reactive([
    { name: '姓名', prop: 'name' },
    { name: '用户ID', prop: 'userId' },
    { name: '职位', prop: 'position' },
    { name: '手机号', prop: 'mobile' },
    { name: '性别', prop: 'gender' },
    { name: '邮箱', prop: 'email' },
    { name: '部门', prop: 'departments' },
    { name: 'Tags', prop: 'tags' },
]);

</script>
<template>
    <div class="layout-page-wrapper">
        <kv-card :info="userStore.userInfo" :columns="columns"></kv-card>
    </div>
</template>
