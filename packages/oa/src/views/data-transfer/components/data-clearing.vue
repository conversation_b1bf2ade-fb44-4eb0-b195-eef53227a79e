<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { H5Form } from '@/vendor/x-form';
import { dataCleaningFormSchema as formSchema } from '@/views/data-transfer/model/schema';
import { Toast } from 'vant';
import { DataTransferApi } from '@/api/data-transfer-api';
import { sleep } from '@/utils/utils';
import { Form } from '@/vendor/x-form/core/form';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus/es';
import DataClearingMatrix = AbcAPI.DataClearingMatrix;
import { TransferTypeEnum } from '@/views/data-transfer/model/model';
import CreateDataClearingRecordDto = AbcAPI.CreateDataClearingRecordDto;
import { useUserStore } from '@/store/user';
import _ from 'lodash';

const dataSource = ref<any>(null);
const route = useRoute();
const operateType = ref('add');
onMounted(async () => {
    await getDataTypes();
    const { transferDataId } = route.query;
    if (transferDataId) {
        operateType.value = 'edit';
        await getTransferDetailById(TransferTypeEnum.DATA_CLEARING, transferDataId);
    } else {
        dataSource.value = {};
    }
});
/**
 * @description: 获取清理单详情
 * @date: 2023-01-16 16:17:47
 * @author: Horace
 * @param type: 清理单类型
 * @param id: 清理单id
 * @return
 */
const getTransferDetailById = async (type: number, id: any) => {
    let res: any = {};
    try {
        res = await DataTransferApi.getApiLowCodeDataTransferRecordDetailById(type, id);
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    dataSource.value = res;
};

let formControl: Form;
const handlePrepared = (form: Form) => {
    formControl = form;
    formControl?.setQuerySchemaOptions('types', dataTypes.value.length ? dataTypes.value : []);
    if (operateType.value === 'edit') {
        formControl?.setQuerySchemaOptions('showTypes', relatedDataTypes.value.length ? relatedDataTypes.value : []);
    }
};
const dataTypes = ref<DataClearingMatrix[]>([]);
/**
 * @description: 获取数据类型
 * @date: 2023-1-11 13:38:36
 * @author: Horace
 * @param null:
 * @return
 */
const getDataTypes = async () => {
    let res: any = {};
    try {
        res = await DataTransferApi.getApiLowCodeDataTransferDataClearingTypeList();
    } catch (e: any) {
        ElMessage.error(e.message || e);
    }
    if (res && res.rows) {
        dataTypes.value = res.rows.map((item: any) => ({
            label: item.dataTypeName,
            value: item.dataTypeId,
            ...item,
        }));
    }
};
const relatedDataTypes = ref<DataClearingMatrix[]>([]);
/**
 * @description: 当清理的数据类型变化关联的数据类型也发生改变
 * @date: 2023-1-11 11:17:38
 * @author: Horace
 * @param data: 表单数据
 * @return
 */
const checkTypes = (data: any) => {
    const { types } = data;
    relatedDataTypes.value = [];
    types?.forEach((item: number) => {
        const type = dataTypes.value.find((type: DataClearingMatrix) => type.dataTypeId === item);
        const { relatedDataTypeIds = [] } = type || {};
        const types = dataTypes.value.filter((type: DataClearingMatrix) => relatedDataTypeIds.includes(type.dataTypeId));
        relatedDataTypes.value = _.uniqBy(relatedDataTypes.value.concat(types), 'dataTypeId');
    });
    relatedDataTypes.value = relatedDataTypes.value?.filter((item: any) => !types.find((type: number) => type === item.dataTypeId));
    formControl?.setQuerySchemaOptions('showTypes', relatedDataTypes.value.length ? relatedDataTypes.value : []);
};
const userStore = useUserStore();
const userInfo = userStore.userInfo;
const router = useRouter();
const showSubmitDialog = ref(false);
const actionData = reactive<any>({
    action: <any>{}, formData: <any>{}, valid: false,
});
/**
 * @description: 打开二次确认弹窗
 * @date: 2023-01-16 16:18:50
 * @author: Horace
 * @param
 * @return
 */
const handleSubmitDialogAction = async () => {
    const { action, formData, valid } = actionData;
    const organInfo: any = formControl.getField('clinicId').getBundle();
    if (action.name === 'submit') {
        if (!organInfo?.name) {
            Toast.fail('请重新选择确认导入门店信息');
            return;
        }
        if (!valid) {
            Toast.fail('请完善表单内容');
            return;
        }
        let res: any = {};
        delete formData.id;
        const postData: CreateDataClearingRecordDto = {
            chainId: organInfo?.chainId || '',
            chainName: organInfo?.chainName || '',
            clinicName: organInfo?.name || '',
            editionName: organInfo?.editionName || '',
            adminName: organInfo?.adminName || '',
            adminMobile: organInfo?.adminMobile || '',
            userName: userInfo.name || '',
            ...formData,
        };
        Toast.loading(`正在${operateType.value === 'add' ? '创建' : '编辑' }数据清理单`);
        const { transferDataId, recordId } = route.query;
        try {
            if (operateType.value === 'add') {
                res = await DataTransferApi.postApiLowCodeDataTransferDataClearingCreate(postData);
            } else {
                res = await DataTransferApi.putApiLowCodeDataTransferDataClearingUpdateById({ ...postData, transferId: recordId + '' }, transferDataId + '');
            }
        } catch (e: any) {
            Toast.fail(e.message || e);
        }
        if (res && res.result) {
            await sleep(2000);
            Toast.success(`${operateType.value === 'add' ? '创建' : '编辑' }单据成功`);
            await router.push({ name: '@data-transfer/record' });
        }
    }
};
/**
 * @description: 提交
 * @date: 2023-01-16 16:19:08
 * @author: Horace
 * @param
 * @return
 */
const handleAction = async ({ action, formData, valid }: any) => {
    if (action.name === 'submit') {
        if (!valid) {
            Toast.fail('请完善表单内容');
            return;
        }
        actionData.action = action || {};
        actionData.formData = formData || {};
        actionData.valid = valid;
        showSubmitDialog.value = true;
    }
};
let oldTypes: any[] = [];
/**
 * @description: 表单变化
 * @date: 2023-01-16 16:19:46
 * @author: Horace
 * @param data: 变化后的表单数据
 * @return
 */
const handleChange = async ({ formData }: any) => {
    if (JSON.stringify(formData.types) !== JSON.stringify(oldTypes)) {
        oldTypes = formData.types;
        await checkTypes(formData);
    }
};
</script>
<template>
    <h5-form
        v-if="dataSource"
        :schema="formSchema"
        :data="dataSource"
        class="data-clearing-wrapper"
        @action="handleAction"
        @prepared="handlePrepared"
        @change="handleChange"
    >
    </h5-form>
    <van-dialog
        v-model:show="showSubmitDialog"
        title=""
        show-cancel-button
        class="submit-dialog"
        @confirm="handleSubmitDialogAction"
    >
        <van-icon class="submit-dialog-icon" :size="64" name="warn-o" />
        <p>我很清楚数据清理风险，确认提交以上清理任务！</p>
    </van-dialog>
</template>
<style lang="scss">
.submit-dialog {
    .submit-dialog-icon {
        color: #d90000;
        padding-bottom: 16px;
    }

    .van-dialog__content {
        text-align: center;
        padding: 24px 24px 24px 36px;
    }
}

.data-clearing-wrapper {
    .van-checkbox-group {
        flex-direction: column;

        .van-checkbox + .van-checkbox {
            margin-top: 12px;
        }
    }

    .data-clear-warning-wrapper {
        .h5-field__label,
        .h5-field__value-wrapper {
            color: #de0c0c;
        }

        .van-checkbox__label.van-checkbox__label--disabled {
            color: #ff9797;
        }

        .van-checkbox__icon.van-checkbox__icon--square.van-checkbox__icon--disabled {
            display: none;
        }
    }
}
</style>
