<script setup lang="ts">
import { CorpAPI } from '@/api/corp-api';
import { ElMessage } from 'element-plus';
import { reactive, ref, toRaw } from 'vue';

const postData = reactive({
    username: '',
    token: '',
});

const formRef = ref<any>();

async function handleSaveClick(formRef: any) {
    await formRef.validate((valid: boolean) => {
        if (valid) {
            return submit();
        }
        return Promise.resolve();
    });
}

async function submit() {
    try {
        await CorpAPI.updateCorpJenkinsTokenUsingPUT(toRaw(postData));
        ElMessage.success('保存成功');
    } catch (e) {
        ElMessage.error('保存失败');
    }
}
</script>
<template>
    <div class="oa-setting-page">
        <el-form ref="formRef" label-position="top" :model="postData">
            <el-form-item label="Jenkins用户名" prop="username" required>
                <el-input v-model="postData.username"></el-input>
            </el-form-item>
            <el-form-item required prop="token">
                <template #label>
                    Token <el-link
                        type="primary"
                        target="_blank"
                        href="https://abcyun.yuque.com/abc-home/ywhr14/mosn4z#JTM4Q）"
                        :underline="false"
                    >
                        生成方法
                    </el-link>
                </template>
                <el-input v-model="postData.token"></el-input>
            </el-form-item>

            <el-button type="primary" @click="handleSaveClick(formRef)">保存</el-button>
        </el-form>
    </div>
</template>

<style lang="scss">
    .oa-setting-page {
        a {
            font-size: var(--oa-font-size-12);
        }
    }
</style>
