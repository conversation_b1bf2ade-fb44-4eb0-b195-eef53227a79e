import { HisType } from '@/utils/clinic';
import { TicketOrderTypeEnum } from '@/utils/ticket';
import ClinicTicketTagView = AbcAPI.ClinicTicketTagView;
import ClinicTicketMsgView = AbcAPI.ClinicTicketMsgView;

export interface TicketForm {
    // 连锁id
    chainId?:string;
    // 门店id
    clinicId?:string;
    // 反馈门店名称
    clinicName?:string;
    // 工单号
    code?:string;
    // 创建时间
    created?:string;
    // 处理人id
    dealerId?:string;
    // 处理人名字
    dealerName?:string;
    // 描述
    description?:string;
    // 反馈客户id
    employeeId?:string;
    // 反馈客户名字
    employeeName?:string;
    // 跟进人id
    followerId?:string;
    // 跟进人姓名
    followerName?:string;
    // 工单id
    id?:string;
    // 根本原因
    reason?:string;
    // 状态
    status?:number;
    // 标签
    tags?:Array<ClinicTicketTagView>;
    // 工单标题
    title?:string;
    // 反馈时间
    feedbackTime?:string;
    // 来源-0：1v1客服，1：外部群，2：客户于系统提单，3：内部人员提单，90其他{@linkClinicTicket.FromWay}
    fromWay?:number;
    // 消息记录
    msgList?:Array<ClinicTicketMsgView>;
    // 备注
    remark?:string;
    // 标签列表
    tagList?:Array<ClinicTicketTagView>
    // tapd 处理人的id
    tapdDealerId?:string;
    // 创建人
    createByName?:string;
    // tapd单链接
    tapdLick?:string;
    type?: TicketOrderTypeEnum | undefined;
    hisType?: HisType | undefined;
    question?: string | undefined;
    tag?: string | undefined;
    owner?: string | undefined;
    isEmergency?: boolean;
}
