<script lang="ts" setup>
import { setFocus } from '@/utils/mobile-select';
import { onMounted, ref, watch } from 'vue';
import { useChatTagHook } from '@/views/client-service/technical-support/hook/chatTag';

const {
    tagId,
    tagList,
    isHighFrequency,
    getTagList,
    handleTagSubmit,
    getKfConversationTag,
} = useChatTagHook();
const props = defineProps({
    conversationId: {
        type: String,
        default: '',
    },
    showHighFrequency: {
        type: Boolean,
        default: false,
    },
});
const handleHighFrequencyChange = () => {
    if (!tagId.value) {
        return;
    }
    handleTagSubmit(tagId.value, props.conversationId);
};
watch(() => props.conversationId, (val) => {
    if (!tagList.value?.length) {
        getTagList();
    }
    if (val) {
        getKfConversationTag(val);
    }
}, {
    immediate: true,
});

const handleTagChange = (val: any) => {
    handleTagSubmit(val, props.conversationId);
};
</script>
<template>
    <el-space :class="{'chat-tag-wrapper': showHighFrequency}">
        <el-select
            v-model="tagId"
            class="filter-select"
            filterable
            clearable
            fit-input-width
            placeholder="请标记此次咨询分类"
            @hoot="setFocus"
            @visible-change="setFocus"
            @change="handleTagChange"
        >
            <el-option
                v-for="item in tagList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
                <p class="ticket-order-select-item flex-between">
                    <span>{{ item.label }}</span>
                    <el-tooltip
                        v-if="item.description"
                        effect="dark"
                        :content="item.description"
                        placement="top"
                    >
                        <span><el-icon><Warning /></el-icon></span>
                    </el-tooltip>
                </p>
            </el-option>
        </el-select>
        <el-checkbox
            v-if="showHighFrequency"
            v-model="isHighFrequency"
            label="高频咨询"
            size="large"
            @change="handleHighFrequencyChange"
        />
    </el-space>
</template>
<style lang="scss">

.chat-tag-wrapper {
    padding: 16px;

    .el-space__item:first-child {
        flex: 1;
    }
}
</style>
