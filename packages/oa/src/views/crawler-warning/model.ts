/**
 * 爬虫预警模型定义
 */

/* eslint-disable @typescript-eslint/no-unused-vars */

// 爬虫预警执行动作枚举
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export enum CrawlerWarningAction {
    // 强制下线
    FORCE_LOGOUT = 'force_logout',
    // 禁用账号
    DISABLE_EMPLOYEE = 'disable_employee',
    // 数据混淆
    DATA_OBFUSCATION = 'data_obfuscation',
    // 观察
    OBSERVE = 'observe',
    // 人机验证
    CAPTCHA = 'captcha',
}

// 爬虫预警动作标签映射
export const actionLabelMap = {
    [CrawlerWarningAction.FORCE_LOGOUT]: '强制下线',
    [CrawlerWarningAction.DISABLE_EMPLOYEE]: '禁用账号',
    [CrawlerWarningAction.DATA_OBFUSCATION]: '数据混淆',
    [CrawlerWarningAction.OBSERVE]: '观察',
    [CrawlerWarningAction.CAPTCHA]: '人机验证',
};

// 限制状态枚举
export enum ClinicLimitStatus {
    // 观察
    OBSERVE = 0,
    // 限制
    LIMIT = 10,
    // 已解除限制
    RELEASED = 90,
}

// 导出枚举值供外部使用，避免lint警告
export const { OBSERVE, LIMIT, RELEASED } = ClinicLimitStatus;

// 限制状态标签映射
export const statusLabelMap = {
    [ClinicLimitStatus.OBSERVE]: '观察',
    [ClinicLimitStatus.LIMIT]: '限制',
    [ClinicLimitStatus.RELEASED]: '已解除限制',
};

// 查询参数接口 - 对应 AbcAPI.QueryAntiBotRecordsReq
export interface CrawlerWarningQueryParams {
    clinicId?: string; // 门店id
    employeeId?: string; // 员工id
    status?: ClinicLimitStatus; // 状态：0观察 10限制 90已解除限制
    action?: string; // 动作 force_logout:强制下线 disable_employee:禁用账号 data_obfuscation:数据混淆 observe:观察 captcha:人机验证
    beginDate?: string; // 开始时间 yyyy-mm-dd hh:mm:ss
    endDate?: string; // 结束时间 yyyy-mm-dd hh:mm:ss
    offset?: number; // 分页参数
    limit?: number; // 分页参数
}

// 爬虫预警信息接口 - 对应 AbcAPI.ClinicLimitRequestLogView
export interface CrawlerWarningInfo {
    id: number; // id
    chainId: string; // 连锁ID
    clinicId: string; // 诊所ID
    clinicName: string; // 门店名称
    clinicExpiredAt: string; // 门店到期时间
    editionName: string; // 门店版本
    employeeId: string; // 员工ID
    employeeName: string; // 员工姓名
    employeeMobile: string; // 员工手机号
    employeeOpenId: string; // 员工微信openid
    path: string; // 请求路径
    reqMethodName: string; // 请求方法名
    remoteIp: string; // 远程IP地址
    status: number; // 状态0观察中10已限制90已解除
    created: string; // 告警时间
    lastReportTime: string; // 前端最后上报时间
    env: string; // 环境
    userAgent: string; // 用户代理
    clientInfo: string; // 客户端信息
    tokenKey: string; // Token键
    uuid: string; // 用户UUID
    addressProvince: string; // 门店所在省
    addressCity: string; // 门店所在市
    addressDistrict: string; // 门店所在区
    ruleResult?: any; // 规则结果(JSON格式)
    unlimitedTime?: string; // 解除限制时间
    // 前端扩展字段
    scope?: string; // scopeType
    scopeId?: string; // scopeId
    action?: string; // 从ruleResult中解析的动作
    requestCount?: number; // 从ruleResult中解析的请求次数
    limitThreshold?: number; // 从ruleResult中解析的限制阈值
    ruleName?: string; // 从ruleResult中解析的规则名称
    sellerName?: string; // 销售人员
    remoteIpAreaAddress?: string; // 远程IP行政区域
}

// 分页响应接口
export interface CrawlerWarningListResponse {
    list: CrawlerWarningInfo[];
    total: number;
}
