<template>
  <div class="asr-audio-player">
    <!-- 音频播放器 -->
    <div class="player-container">
      <div class="player-controls">
        <!-- 播放/暂停按钮 -->
        <el-button
          :icon="isPlaying ? VideoPause : VideoPlay"
          circle
          size="large"
          type="primary"
          @click="togglePlay"
        />

        <!-- 快退按钮 -->
        <el-button
          icon="DArrowLeft"
          circle
          @click="skipBackward"
          title="快退5秒"
        />

        <!-- 快进按钮 -->
        <el-button
          icon="DArrowRight"
          circle
          @click="skipForward"
          title="快进5秒"
        />
      </div>

      <!-- 进度条 -->
      <div class="progress-container">
        <span class="time-display">{{ formatTime(currentTime) }}</span>
        <el-slider
          v-model="currentTime"
          :max="duration"
          :step="0.1"
          :show-tooltip="false"
          @input="seekTo"
          class="progress-slider"
        />
        <span class="time-display">{{ formatTime(duration) }}</span>
      </div>

      <!-- 播放速度控制 -->
      <div class="speed-control">
        <span class="speed-label">播放速度：</span>
        <el-select
          v-model="playbackRate"
          @change="changePlaybackRate"
          size="small"
          style="width: 80px;"
        >
          <el-option
            v-for="option in playbackRateOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>

      <!-- 音量控制 -->
      <div class="volume-control">
        <el-icon><Microphone /></el-icon>
        <el-slider
          v-model="volume"
          :max="100"
          :show-tooltip="false"
          @change="changeVolume"
          style="width: 100px; margin-left: 8px;"
        />
      </div>
    </div>

    <!-- 隐藏的audio元素 -->
    <audio
      ref="audioRef"
      :src="audioUrl"
      @loadedmetadata="onLoadedMetadata"
      @timeupdate="onTimeUpdate"
      @ended="onEnded"
      @error="onError"
      preload="metadata"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { VideoPlay, VideoPause, Microphone } from '@element-plus/icons-vue';
import { PLAYBACK_RATE_OPTIONS } from '../constants';

interface Props {
  audioUrl: string;
  audioName?: string;
  autoPlay?: boolean;
}

interface Emits {
  (e: 'timeUpdate', time: number): void;
  (e: 'durationChange', duration: number): void;
  (e: 'playStateChange', isPlaying: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  audioName: '音频文件',
  autoPlay: false,
});

const emit = defineEmits<Emits>();

// 音频元素引用
const audioRef = ref<HTMLAudioElement>();

// 播放状态
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const playbackRate = ref(1.0);
const volume = ref(100);

// 播放速度选项
const playbackRateOptions = PLAYBACK_RATE_OPTIONS;

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 播放/暂停切换
const togglePlay = () => {
  if (!audioRef.value) return;

  if (isPlaying.value) {
    audioRef.value.pause();
  } else {
    audioRef.value.play().catch(error => {
      console.error('播放失败:', error);
      ElMessage.error('音频播放失败');
    });
  }
};

// 快退5秒
const skipBackward = () => {
  if (!audioRef.value) return;
  audioRef.value.currentTime = Math.max(0, audioRef.value.currentTime - 5);
};

// 快进5秒
const skipForward = () => {
  if (!audioRef.value) return;
  audioRef.value.currentTime = Math.min(duration.value, audioRef.value.currentTime + 5);
};

// 跳转到指定时间
const seekTo = (time: number) => {
  if (!audioRef.value) return;
  audioRef.value.currentTime = time;
};

// 改变播放速度
const changePlaybackRate = (rate: number) => {
  if (!audioRef.value) return;
  audioRef.value.playbackRate = rate;
};

// 改变音量
const changeVolume = (vol: number) => {
  if (!audioRef.value) return;
  audioRef.value.volume = vol / 100;
};

// 音频事件处理
const onLoadedMetadata = () => {
  if (!audioRef.value) return;
  duration.value = audioRef.value.duration;
  emit('durationChange', duration.value);
};

const onTimeUpdate = () => {
  if (!audioRef.value) return;
  currentTime.value = audioRef.value.currentTime;
  emit('timeUpdate', currentTime.value);
};

const onEnded = () => {
  isPlaying.value = false;
  emit('playStateChange', false);
};

const onError = (error: Event) => {
  console.error('音频加载错误:', error);
  ElMessage.error('音频加载失败，请检查文件路径');
};

// 监听播放状态变化
watch(isPlaying, (newVal) => {
  emit('playStateChange', newVal);
});

// 监听音频URL变化
watch(() => props.audioUrl, () => {
  if (audioRef.value) {
    audioRef.value.load();
    isPlaying.value = false;
    currentTime.value = 0;
  }
});

// 组件挂载时的处理
onMounted(() => {
  if (audioRef.value) {
    // 监听播放状态变化
    audioRef.value.addEventListener('play', () => {
      isPlaying.value = true;
    });

    audioRef.value.addEventListener('pause', () => {
      isPlaying.value = false;
    });

    // 设置初始音量
    audioRef.value.volume = volume.value / 100;

    // 如果需要自动播放
    if (props.autoPlay) {
      audioRef.value.play().catch(error => {
        console.error('自动播放失败:', error);
      });
    }
  }
});

// 组件卸载时清理
onUnmounted(() => {
  if (audioRef.value) {
    audioRef.value.pause();
    audioRef.value.src = '';
  }
});

// 暴露方法给父组件
defineExpose({
  play: () => audioRef.value?.play(),
  pause: () => audioRef.value?.pause(),
  seekTo,
  getCurrentTime: () => currentTime.value,
  getDuration: () => duration.value,
  isPlaying: () => isPlaying.value,
});
</script>

<style lang="scss" scoped>
.asr-audio-player {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .1);

    .player-container {
        display: flex;
        align-items: center;
        gap: 16px;

        .player-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .progress-container {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;

            .time-display {
                font-size: 14px;
                color: #666;
                min-width: 45px;
                text-align: center;
            }

            .progress-slider {
                flex: 1;
            }
        }

        .speed-control {
            display: flex;
            align-items: center;
            gap: 8px;

            .speed-label {
                font-size: 14px;
                color: #666;
            }
        }

        .volume-control {
            display: flex;
            align-items: center;
        }
    }

    .audio-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #666;

        .current-info {
            font-weight: 500;
        }

        .audio-name {
            color: #409eff;
        }
    }
}

// 移动端适配
@media (max-width: 768px) {
    .asr-audio-player {
        padding: 16px;

        .player-container {
            flex-direction: column;
            gap: 12px;

            .progress-container {
                width: 100%;
            }

            .speed-control,
            .volume-control {
                width: 100%;
                justify-content: center;
            }
        }

        .audio-info {
            flex-direction: column;
            gap: 8px;
            text-align: center;
        }
    }
}
</style>
