<script setup lang="ts">
import { onMounted, ref, nextTick, defineAsyncComponent, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useFormat } from '@/composables/date';
import _ from 'lodash';
import * as echarts from 'echarts';
import { useThemeConfigStore } from '@/store/theme-config';
import dayjs from 'dayjs';
import useArea from '@/composables/area';
import { CsSummaryAPI } from '@/api/cs-summary-api';

const TicketDetail = defineAsyncComponent(() => import('@/views/after-ticket/components/detail.vue'));
const GroupConsultSummaryMobile = defineAsyncComponent(() => import('@/views/after-sales/customer-problems-summary/mobile-mode/index.vue'));
const ExclusiveGroupDetail = defineAsyncComponent(() => import('@/views/after-sales/customer-problems-summary/exclusive-group-detail.vue'));

const themeConfig = useThemeConfigStore();

const cascaderProps = {
    multiple: true,
    value: 'code',
    label: 'name',
    children: 'children',
};

// 图表实例
let firstChartInstance: echarts.ECharts | null = null;
let secondChartInstance: echarts.ECharts | null = null;

// 问题类型TOP20数据
const firstChartData = ref<any[]>([]);
const secondChartData = ref<any[]>([]);
const visibleTicketDetailDialog = ref(false);

// 获取昨天的日期
const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD');

// 日期范围
const dateRange = ref<[string, string]>([yesterday, yesterday]);

// 地区数据和筛选
const { areaTree } = useArea();
const selectedAreas = ref<string[][]>([]);

// 处理地区数据，只保留省市两级
const provinceCityOptions = computed(() => areaTree.value.map(province => ({
    ...province,
    children: province.children?.map(city => ({
        ...city,
        children: undefined, // 移除区县级别
    })),
})));

// 表单数据
const query = reactive({
    beginDate: yesterday,
    endDate: yesterday,
    firstCategory: undefined,
    secondCategory: undefined,
    addressCityCodeList: [] as string[],
    addressProvinceCodeList: [] as string[],
    qwRoomNameKeyword: '',
});

// 表格数据
const tableData = ref<any[]>([]);
const tableLoading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);

/**
 * @description: 获取echarts数据
 * @Date: 2025-08-21 16:03:33
 * @author: yaoyongpeng
 * @return {*}
 */
const getFirstChartData = async () => {
    const topNum = 50;
    try {
        const res: any = await CsSummaryAPI.getChatTagTopUsingGET(
            topNum,
            query.endDate,
            undefined,
            query.beginDate,
        );
        firstChartData.value = res?.rows?.map((item: any) => ({
            ...item,
            name: item.tagName,
            value: item.count,
        }));
        query.firstCategory = firstChartData.value[0]?.tagId;
        await getSecondChartData();
    } catch (err: any) {
        ElMessage({
            type: 'error',
            message: err.message || err,
        });
    }
};

const getSecondChartData = async () => {
    const topNum = 50;
    try {
        const res: any = await CsSummaryAPI.getChatTagTopUsingGET(
            topNum,
            query.endDate,
            query.firstCategory,
            query.beginDate,
        );
        secondChartData.value = res?.rows?.map((item: any) => ({
            ...item,
            name: item.tagName,
            value: item.count,
        }));
        query.secondCategory = secondChartData.value[0]?.tagId ?? query.firstCategory; // 如果没有二级分类，使用一级分类搜索
        initSecondChart();
        getTableData();
    } catch (err: any) {
        ElMessage({
            type: 'error',
            message: err.message || err,
        });
    }
};

// 初始化图表
const initChart = async () => {
    await getFirstChartData();
    // 获取 query-table__pc-wrapper 元素
    nextTick(() => {
        const wrapper = document.querySelector('.customer-problems-summary__content');
        if (!wrapper) {
            console.error('找不到内容容器元素');
            return;
        }

        // 创建图表容器
        let chartContainer = wrapper.querySelector('.chart-container');

        // 初始化图表
        if (firstChartInstance) {
            firstChartInstance.dispose();
        }
        
        // 根据数据情况调整容器高度
        (chartContainer as HTMLElement).style.minHeight = '75vh';
        
        firstChartInstance = echarts.init(chartContainer as HTMLElement);
        
        // 设置图表选项
        const option: echarts.EChartsOption = !firstChartData.value || firstChartData.value.length === 0
            ? {
                title: {
                    text: '一级分类榜',
                    left: 'center',
                    textStyle: {
                        fontWeight: 'normal',
                    },
                },
                graphic: {
                    elements: [{
                        type: 'text',
                        style: {
                            text: '暂无数据',
                            font: '14px sans-serif',
                            fill: '#999',
                        },
                        left: 'center',
                        top: 'center',
                    }],
                },
            }
            : {
                title: {
                    text: '一级分类榜',
                    left: 'center',
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                    },
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true,
                },
                dataZoom: [
                    {
                        type: 'inside',
                        orient: 'vertical',
                        start: 0,
                        end: 100,
                    },
                ],
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01],
                    axisLabel: {
                        rotate: 0,
                        margin: 8,
                    },
                    splitNumber: 4,
                    minInterval: 1,
                },
                yAxis: {
                    type: 'category',
                    data: firstChartData.value.map(item => item.name),
                    inverse: true,
                    axisTick: {
                        alignWithLabel: true,
                    },
                    axisLabel: {
                        interval: 0,
                        margin: 8,
                        fontSize: 12,
                        lineHeight: 20,
                    },
                },
                series: [
                    {
                        name: '咨询数量',
                        type: 'bar',
                        barWidth: '60%',
                        itemStyle: {
                            color: '#409EFF',
                        },
                        label: {
                            show: true,
                            position: 'right',
                            formatter: '{c}',
                        },
                        data: firstChartData.value.map(item => item.value),
                    },
                ],
            };
        
        firstChartInstance.setOption(option);
        
        // 添加点击事件
        firstChartInstance.on('click', (params: any) => {
            // 获取点击的数据项
            const clickedData = firstChartData.value[params.dataIndex];
            query.firstCategory = clickedData.tagId;
            
            // 重新获取二级分类数据
            getSecondChartData();
        });
        
        window.addEventListener('resize', () => {
            firstChartInstance?.resize();
        });
    });
};

// 初始化第二个图表
const initSecondChart = async () => {
    // 获取 query-table__pc-wrapper 元素
    nextTick(() => {
        const wrapper = document.querySelector('.customer-problems-summary__content');
        if (!wrapper) {
            console.error('找不到内容容器元素');
            return;
        }

        // 创建图表容器
        let chartContainer = wrapper.querySelector('.second-chart-container');

        // 初始化图表
        if (secondChartInstance) {
            secondChartInstance.dispose();
        }
        
        // 根据数据情况调整容器高度
        (chartContainer as HTMLElement).style.minHeight = '75vh';
        
        secondChartInstance = echarts.init(chartContainer as HTMLElement);
        
        // 设置图表选项
        const option: echarts.EChartsOption = !secondChartData.value || secondChartData.value.length === 0
            ? {
                title: {
                    text: '二级分类榜',
                    left: 'center',
                    textStyle: {
                        fontWeight: 'normal',
                    },
                },
                graphic: {
                    elements: [{
                        type: 'text',
                        style: {
                            text: '暂无数据',
                            font: '14px sans-serif',
                            fill: '#999',
                        },
                        left: 'center',
                        top: 'center',
                    }],
                },
            }
            : {
                title: {
                    text: '二级分类榜',
                    left: 'center',
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                    },
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true,
                },
                dataZoom: [
                    {
                        type: 'inside',
                        orient: 'vertical',
                        start: 0,
                        end: 100,
                    },
                ],
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01],
                    axisLabel: {
                        rotate: 0,
                        margin: 8,
                    },
                    splitNumber: 4,
                    minInterval: 1,
                },
                yAxis: {
                    type: 'category',
                    data: secondChartData.value.map(item => item.name),
                    inverse: true,
                    axisTick: {
                        alignWithLabel: true,
                    },
                    axisLabel: {
                        interval: 0,
                        margin: 8,
                        fontSize: 12,
                        lineHeight: 20,
                    },
                },
                series: [
                    {
                        name: '咨询数量',
                        type: 'bar',
                        barWidth: '60%',
                        itemStyle: {
                            color: '#67C23A',
                        },
                        label: {
                            show: true,
                            position: 'right',
                            formatter: '{c}',
                        },
                        data: secondChartData.value.map(item => item.value),
                    },
                ],
            };
        
        secondChartInstance.setOption(option);
        
        // 添加点击事件
        secondChartInstance.on('click', (params: any) => {
            // 获取点击的数据项
            const clickedData = secondChartData.value[params.dataIndex];
            query.secondCategory = clickedData.tagId;
            
            // 刷新表格数据
            getTableData();
        });
        
        window.addEventListener('resize', () => {
            secondChartInstance?.resize();
        });
    });
};

/**
 * @description: 获取表格数据
 * @Date: 2025-08-21 14:39:55
 * @author: yaoyongpeng
 * @return {*}
 */
// 获取表格数据
const getTableData = async () => {
    tableLoading.value = true;
    try {
        const offset = (currentPage.value - 1) * pageSize.value;
        const res: any = await CsSummaryAPI.getChatListUsingPOST({
            endDate: query.endDate,
            startDate: query.beginDate,
            limit: pageSize.value,
            addressCityCodeList: query.addressCityCodeList,
            addressProvinceCodeList: query.addressProvinceCodeList,
            qwRoomNameKeyword: query.qwRoomNameKeyword,
            offset,
            tagId: query.secondCategory,
        });
        
        if (res && res.rows) {
            // 格式化数据
            tableData.value = res.rows?.map((row: any) => {
                // 处理地址信息
                const addresses = row.clinicList?.map((clinic: any) => `${clinic.addressProvinceName}·${clinic.addressCityName}`).join('、') || '';
                
                // 处理门店名称
                const clinicNames = row.clinicList?.map((clinic: any) => clinic.name).join('、') || '';
                
                return {
                    ...row,
                    address: addresses,
                    clinicNames,
                };
            }) || [];
            total.value = res.total;
        } else {
            tableData.value = [];
            total.value = 0;
        }
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || e,
        });
        tableData.value = [];
        total.value = 0;
    } finally {
        tableLoading.value = false;
    }
};

// 搜索
const handleSearch = async () => {
    currentPage.value = 1;
    await initChart();
};

// 分页变化
const handleCurrentChange = (page: number) => {
    currentPage.value = page;
    getTableData();
};

const handleSizeChange = (size: number) => {
    pageSize.value = size;
    currentPage.value = 1;
    getTableData();
};

onMounted(async () => {
    if (!themeConfig.isMobile) {
        await initChart();
        await initSecondChart();
    }
});

const exclusiveGroupDetailDialog = ref(false);
const currentRow = ref<any>({});

/**
 * 查看详情
 * @param row
 */
const onHandleView = (row: any) => {
    currentRow.value = _.cloneDeep(row || {});
    if (row.summaryId) {
        exclusiveGroupDetailDialog.value = true;
    } else {
        currentRow.value.auditResults = [];
    }
};

const createTicket = () => {
    visibleTicketDetailDialog.value = true;
};

const formatReceptionMemberNames = (receptionMemberNames: any) => {
    let str = '';
    receptionMemberNames?.forEach((item: any, index: number) => {
        str += item?.name + (index === receptionMemberNames.length - 1 ? '' : '、');
    });
    return str;
};

const formatTags = (tags: any) => {
    if (!tags || tags.length === 0) return '';
    
    // 找到匹配 query.secondCategory 的项
    const matchedItem = tags.find((item: any) => item?.id === query.secondCategory);
    
    // 过滤出不匹配的项
    const otherItems = tags.filter((item: any) => item?.id !== query.secondCategory);
    
    // 构建结果数组，匹配的项放在最前面
    const sortedTags = matchedItem ? [matchedItem, ...otherItems] : tags;
    
    // 拼接字符串
    return sortedTags.map((item: any) => item?.name).join('、');
};

// 处理日期范围变化
const handleDateRangeChange = (dates: [string, string] | null) => {
    if (dates && dates.length === 2) {
        query.beginDate = dates[0];
        query.endDate = dates[1];
    } else {
        query.beginDate = '';
        query.endDate = '';
    }
    handleSearch();
};

// 处理地区筛选变化
const handleAreaChange = (value: string[][]) => {
    selectedAreas.value = value;
    
    // 分离省份和城市代码
    const provinceCodeList: string[] = [];
    const cityCodeList: string[] = [];
    
    value.forEach(path => {
        // 选择了省份和城市
        provinceCodeList.push(path[0]);
        cityCodeList.push(path[1]);
    });
    
    // 去重
    query.addressProvinceCodeList = [...new Set(provinceCodeList)];
    query.addressCityCodeList = [...new Set(cityCodeList)];

    getTableData();
};

// 处理群名搜索
const handleRoomNameSearch = () => {
    getTableData();
};

</script>
<template>
    <div class="customer-problems-summary__wrapper">
        <div v-if="!themeConfig.isMobile" class="customer-problems-summary__content">
            <!-- 查询表单 -->
            <el-card style="margin-bottom: 10px;">
                <el-form :model="query" inline class="query-form">
                    <el-form-item label="时间范围">
                        <el-date-picker
                            v-model="dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 300px;"
                            @change="handleDateRangeChange"
                        />
                    </el-form-item>
                </el-form>
            </el-card>

            <div class="pc-content">
                <div class="chart-container"></div>
                <div class="second-chart-container"></div>
                <!-- 数据表格 -->
                <div class="table-container">
                    <div class="search-container">
                        <el-form-item label="地区">
                            <el-cascader
                                v-model="selectedAreas"
                                popper-class="customer-problems-summary__cascader-popper"
                                :options="provinceCityOptions"
                                :props="cascaderProps"
                                placeholder="请选择省市"
                                clearable
                                collapse-tags
                                collapse-tags-tooltip
                                style="width: 230px;"
                                size="mini"
                                @change="handleAreaChange"
                            />
                        </el-form-item>
                        <el-form-item label="群聊名称">
                            <el-input
                                v-model="query.qwRoomNameKeyword"
                                placeholder="请输入"
                                clearable
                                size="mini"
                                style="width: 200px;"
                                @blur="handleRoomNameSearch"
                                @keydown.enter="handleRoomNameSearch"
                                @clear="handleRoomNameSearch"
                            />
                        </el-form-item>
                    </div>
                    
                    <el-table
                        v-loading="tableLoading"
                        :data="tableData"
                        height="calc(100% - 95px)"
                        border
                        stripe
                    >
                        <el-table-column prop="tags" label="咨询二级分类" width="150">
                            <template #default="{ row }">
                                <el-tooltip
                                    effect="dark"
                                    :popper-options="{
                                        modifiers: [
                                            {
                                                name: 'flip',
                                                enabled: true,
                                                options: {
                                                    fallbackPlacements: ['top', 'right', 'left', 'bottom'],
                                                }
                                            }
                                        ]
                                    }"
                                    trigger="click"
                                >
                                    <template #default>
                                        <span class="truncate-text">{{ formatTags(row.tags) }}</span>
                                    </template>
                                    <template #content>
                                        {{ formatTags(row.tags) }}
                                    </template>
                                </el-tooltip>
                            </template>
                        </el-table-column>

                        <el-table-column prop="address" label="地区" width="130">
                            <template #default="{ row }">
                                <el-tooltip
                                    effect="dark"
                                    :popper-options="{
                                        modifiers: [
                                            {
                                                name: 'flip',
                                                enabled: true,
                                                options: {
                                                    fallbackPlacements: ['top', 'right', 'left', 'bottom'],
                                                }
                                            }
                                        ]
                                    }"
                                    trigger="click"
                                >
                                    <template #default>
                                        <span class="truncate-text">{{ row.address }}</span>
                                    </template>
                                    <template #content>
                                        {{ row.address }}
                                    </template>
                                </el-tooltip>
                            </template>
                        </el-table-column>

                        <el-table-column prop="qwRoomName" label="专属群名称" width="130" />
                    
                        <el-table-column prop="clinicNames" label="门店名称" width="130">
                            <template #default="{ row }">
                                <el-tooltip
                                    effect="dark"
                                    :popper-options="{
                                        modifiers: [
                                            {
                                                name: 'flip',
                                                enabled: true,
                                                options: {
                                                    fallbackPlacements: ['top', 'right', 'left', 'bottom'],
                                                }
                                            }
                                        ]
                                    }"
                                    trigger="click"
                                >
                                    <template #default>
                                        <span class="truncate-text">{{ row.clinicNames }}</span>
                                    </template>
                                    <template #content>
                                        {{ row.clinicNames }}
                                    </template>
                                </el-tooltip>
                            </template>
                        </el-table-column>

                        <el-table-column prop="summaryDate" label="接待日期" width="100">
                            <template #default="{ row }">
                                {{ useFormat(row.summaryDate, 'YYYY-MM-DD') }}
                            </template>
                        </el-table-column>
                    
                        <el-table-column prop="receptionMemberNames" label="接待人" width="80">
                            <template #default="{ row }">
                                <el-tooltip
                                    effect="dark"
                                    :popper-options="{
                                        modifiers: [
                                            {
                                                name: 'flip',
                                                enabled: true,
                                                options: {
                                                    fallbackPlacements: ['top', 'right', 'left', 'bottom'],
                                                }
                                            }
                                        ]
                                    }"
                                    trigger="click"
                                >
                                    <template #default>
                                        <span class="truncate-text">{{ formatReceptionMemberNames(row.receptionMemberNames) }}</span>
                                    </template>
                                    <template #content>
                                        {{ formatReceptionMemberNames(row.receptionMemberNames) }}
                                    </template>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    
                        <el-table-column prop="question" label="主要问题（AI总结）">
                            <template #default="{ row }">
                                <el-tooltip
                                    effect="dark"
                                    :popper-options="{
                                        modifiers: [
                                            {
                                                name: 'flip',
                                                enabled: true,
                                                options: {
                                                    fallbackPlacements: ['top', 'right', 'left', 'bottom'],
                                                }
                                            }
                                        ]
                                    }"
                                    trigger="click"
                                >
                                    <template #default>
                                        <span class="truncate-text">{{ row.question }}</span>
                                    </template>
                                    <template #content>
                                        <span v-html="row.question"></span>
                                    </template>
                                </el-tooltip>
                            </template>
                        </el-table-column>

                        <el-table-column prop="solution" label="解决方案（AI总结）">
                            <template #default="{ row }">
                                <el-tooltip
                                    effect="dark"
                                    :popper-options="{
                                        modifiers: [
                                            {
                                                name: 'flip',
                                                enabled: true,
                                                options: {
                                                    fallbackPlacements: ['top', 'right', 'left', 'bottom'],
                                                }
                                            }
                                        ]
                                    }"
                                    trigger="click"
                                >
                                    <template #default>
                                        <span class="truncate-text">{{ row.solution }}</span>
                                    </template>
                                    <template #content>
                                        <span v-html="row.solution"></span>
                                    </template>
                                </el-tooltip>
                            </template>
                        </el-table-column>      
                        <el-table-column label="操作" width="100" fixed="right">
                            <template #default="{ row }">
                                <div class="operation">
                                    <el-button size="small" @click="onHandleView(row)">详情</el-button>
                                    <el-button type="primary" size="small" @click="createTicket()">提工单</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                
                    <!-- 分页 -->
                    <div class="pagination-container">
                        <el-pagination
                            :current-page="currentPage"
                            :page-size="pageSize"
                            :total="total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </div>
            </div>
        </div>
        <group-consult-summary-mobile v-else></group-consult-summary-mobile>
        <ticket-detail
            v-if="visibleTicketDetailDialog"
            v-model:visible="visibleTicketDetailDialog"
        ></ticket-detail>
        <exclusive-group-detail
            v-if="exclusiveGroupDetailDialog"
            v-model:visible="exclusiveGroupDetailDialog"
            :current-row="currentRow"
        ></exclusive-group-detail>
    </div>
</template>

<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.customer-problems-summary__wrapper {
    .query-form {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        align-items: baseline;
        min-height: 50px;
    }

    .pc-content {
        display: grid;
        grid-template-columns: 1fr 1fr 3fr;
        grid-gap: 10px;
        height: calc(100vh - 145px);
        max-width: 100%;
        overflow: hidden;

        .no-data {
            text-align: center;
            color: #999;
            height: 500px;
            line-height: 500px;
        }

        .el-input__inner {
            height: 30px !important;
        }
    }

    .truncate-text {
        cursor: pointer;

        @include mixins.text-ellipsis();
    }

    .el-col .el-form-item .el-form-item__content {
        .el-input {
            width: 80%;
        }

        .el-date-editor--daterange {
            flex: .8;
        }
    }

    .audit-level-tags {
        .el-tag + .el-tag {
            margin-top: 4px;
        }
    }

    .table-container {
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
        min-width: 0; /* 允许容器收缩 */
        overflow: hidden;

        .search-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            height: 46px;
        }

        .el-table {
            width: 100%;
        }

        .pagination-container {
            margin-top: 15px;
            text-align: right;
        }

        .operation {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
    }

    .chart-container {
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    }

    .second-chart-container {
        background-color: #fff;
        padding: 15px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    }
}

.customer-problems-summary__cascader-popper {
    .el-checkbox {
        margin-right: 0 !important;
    }
}
</style>

<style lang="scss" scoped>
.customer-problems-summary__wrapper {
    :deep(.el-radio) {
        margin-right: 10px !important;
    }

    :deep(.el-button+.el-button) {
        margin-left: 0 !important;
    }
}
</style>