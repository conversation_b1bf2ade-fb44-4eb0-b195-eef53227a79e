import { PermissionOrderCreateSet, PermissionOrderViewSet, PermissionOrderTestViewSet, PermissionOrderCloudExamCreateSet } from '@/views/order/permission';
import { RouteRecordRaw } from 'vue-router';

const MobileEntry = () => import('@/layout/components/mobile-entry.vue');
const CreateEntry = () => import('./create/entry.vue');
const Create = () => import('./create/create.vue');
const Coupon = () => import('./create/coupon.vue');
const ChuTianYun = () => import('./create/chutianyun.vue');
const Renewal = () => import('./create/renewal.vue');
const Upgrade = () => import('./create/upgrade.vue');
const Downgrade = () => import('./create/downgrade.vue');
const ChainSubToSingle = () => import('./create/chain-sub-to-single.vue');
const Other = () => import('./create/other.vue');
const ModifyPeriod = () => import('./create/modify-period.vue');
const AdditionalAccount = () => import('./create/additional-account.vue');
const buyExaminationDevice = () => import('./create/buy-examination-device.vue');
const ApplyTrial = () => import('./create/apply-trial.vue');
const Sop = () => import('./sop/index.vue');
const SopDetail = () => import('./sop/sop-detail.vue');
const SopSign = () => import('./sop/sop-sign.vue');
const Pay = () => import('./detail/pay.vue');
const Detail = () => import('./detail/detail.vue');
const Audit = () => import('./audit/audit.vue');
const Submit = () => import('./submit/submit.vue');
const Result = () => import('./result/result.vue');
const CloudExamCreate = () => import('./create/cloud-exam-create.vue');

export default [
    {
        path: '/order',
        name: '@order',
        component: MobileEntry,
        meta: {
            isEntry: true,
            name: '订单管理',
            icon: 'document-add',
            roles: PermissionOrderViewSet,
        },
        redirect: {
            path: '/order/entry',
        },
        children: [
            {
                path: 'new',
                name: '@order/new',
                component: CreateEntry,
                alias: '/order/entry',
                meta: {
                    isEntry: true,
                    name: '新订单',
                    icon: 'document-add',
                    roles: PermissionOrderCreateSet,
                },
                children: [
                    {
                        path: 'create',
                        name: '@order/create',
                        component: Create,
                        meta: {
                            name: '版本新购',
                            roles: PermissionOrderTestViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'chutianyun',
                        name: '@order/chutianyun',
                        component: ChuTianYun,
                        meta: {
                            name: '楚天云新购',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'renewal',
                        name: '@order/renewal',
                        component: Renewal,
                        meta: {
                            name: '版本续费',
                            roles: PermissionOrderTestViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'upgrade',
                        name: '@order/upgrade',
                        component: Upgrade,
                        meta: {
                            name: '版本升级',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'downgrade',
                        name: '@order/downgrade',
                        component: Downgrade,
                        meta: {
                            name: '版本降级',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'other',
                        name: '@order/other',
                        component: Other,
                        meta: {
                            name: '其他收费',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'modify-period',
                        name: '@order/modify-period',
                        component: ModifyPeriod,
                        meta: {
                            name: '修改时长',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'coupon',
                        name: '@order/coupon',
                        component: Coupon,
                        meta: {
                            name: '发放优惠券',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    // {
                    //     path: 'chain-sub-to-single',
                    //     name: '@order/chain-sub-to-single',
                    //     component: ChainSubToSingle,
                    //     meta: {
                    //         name: '连锁子店转单店',
                    //         roles: PermissionOrderViewSet,
                    //         hideTabBar: true, // 隐藏 tabBar
                    //     },
                    // },
                    {
                        path: 'additional-account',
                        name: '@order/additional-account',
                        component: AdditionalAccount,
                        meta: {
                            name: '账号增购',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'buy-examination-device',
                        name: '@order/buy-examination-device',
                        component: buyExaminationDevice,
                        meta: {
                            name: '检验设备采购',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'apply-trial',
                        name: '@order/apply-trial',
                        component: ApplyTrial,
                        meta: {
                            name: '申请试用',
                            roles: PermissionOrderTestViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'sop',
                        name: '@order/sop',
                        component: Sop,
                        meta: {
                            name: '个人SOP',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                    {
                        path: 'sop-detail',
                        name: '@order/sop-detail',
                        component: SopDetail,
                        meta: {
                            name: 'SOP详情页',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                            hidden: true, // 侧边栏隐藏
                        },
                    },
                    {
                        path: 'sop-sign',
                        name: '@order/sop-sign',
                        component: SopSign,
                        meta: {
                            name: 'SOP签名页',
                            roles: PermissionOrderViewSet,
                            hideTabBar: true, // 隐藏 tabBar
                            hidden: true, // 侧边栏隐藏
                        },
                    },
                    {
                        path: 'cloud-exam-create',
                        name: '@order/cloud-exam-create',
                        component: CloudExamCreate,
                        meta: {
                            name: 'ABC云检版',
                            roles: PermissionOrderCloudExamCreateSet,
                            hideTabBar: true, // 隐藏 tabBar
                        },
                    },
                ],
            },
            {
                path: 'my-audit',
                name: '@order/audit',
                component: Audit,
                alias: '/order/entry',
                meta: {
                    name: '我审批的',
                    roles: PermissionOrderViewSet,
                },
            },
            {
                path: 'my-submit',
                name: '@order/submit',
                component: Submit,
                meta: {
                    name: '我提交的',
                    roles: PermissionOrderViewSet,
                },
            },
            {
                path: 'pay/:id',
                name: '@order/pay',
                component: Pay,
                props: true,
                meta: {
                    name: '产品订单',
                    roles: PermissionOrderViewSet,
                    hideTabBar: true, // 隐藏 tabBar
                    hidden: true, // 侧边栏隐藏
                },
            },
            {
                path: 'detail/:id',
                name: '@order/detail',
                component: Detail,
                props: true,
                meta: {
                    name: '产品订单',
                    roles: PermissionOrderViewSet,
                    hideTabBar: true, // 隐藏 tabBar
                    hidden: true, // 侧边栏隐藏
                },
            },
            {
                path: 'result/:id',
                name: '@order/result',
                component: Result,
                props: true,
                meta: {
                    name: '提交结果',
                    roles: PermissionOrderViewSet,
                    hideTabBar: true, // 隐藏 tabBar
                    hidden: true, // 侧边栏隐藏
                },
            },
        ],
    },
] as RouteRecordRaw[];
