<script setup lang="ts">
import { OaCard } from '@abc-oa/components';
import OaCellGroup from '@/components/oa-cell-group.vue';
import OaCell from '@/components/oa-cell.vue';
import OrderStatusTag from '@/views/order/components/order-status-tag.vue';
import { formatMoney, formatEdition, formatTime } from '@/utils/format';

const props = defineProps({
    orderInfo: {
        type: Object,
        default: null,
    },
});
</script>
<template>
    <oa-card class="order-summary-create-chain">
        <div class="title-wrapper">
            <span class="name">{{ orderInfo.title }}</span>
            <order-status-tag :status="orderInfo.status" :status-name="orderInfo.statusName"></order-status-tag>
            <span class="time">{{ formatTime(orderInfo.created) }}</span>
        </div>
        <oa-cell-group label-width="90px" size="small" :border="false">
            <oa-cell label="订单内容">【新开连锁】</oa-cell>
            <oa-cell label="订单编号">{{ orderInfo.id }}</oa-cell>
            <oa-cell label="销售">{{ orderInfo.createdByName }}</oa-cell>
        </oa-cell-group>
    </oa-card>
</template>

<style lang="scss">
    .order-summary-create-chain {
        padding-bottom: 0;

        @import "./order-summary-common";
    }
</style>
