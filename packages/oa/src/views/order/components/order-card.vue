<script setup lang="ts">
import failMarkImg from '@/assets/fail-mark.png';
import successMarkImg from '@/assets/success-mark.png';
import { OaCard } from '@abc-oa/components';
import OaCellGroup from '@/components/oa-cell-group.vue';
import OaCell from '@/components/oa-cell.vue';
import { formatMoney } from '@/utils/format';
import { OrderStatus, OrderType } from '@/views/order/model/model';
import { computed } from 'vue';

const props = defineProps({
    orderInfo: {
        type: Object,
        default: null,
    },
});

// 已支付
const isPaid = computed(() => props.orderInfo?.status === OrderStatus.DONE);
// 已取消
const isCanceled = computed(() => props.orderInfo?.status === OrderStatus.CANCELED);

const isNewPurchaseOrder = computed(() => [OrderType.TRIAL_TO_NORMALIZE, OrderType.CREATE_CLINIC].includes(props.orderInfo?.orderType));

const statusMarkImg = computed(() => {
    if (isPaid.value) {
        return successMarkImg;
    } if (isCanceled.value) {
        return failMarkImg;
    }
    return null;
});
</script>
<template>
    <oa-card class="order-detail-card">
        <oa-card class="order-desc-card">
            <img class="logo" src="@/assets/logo-cloud.png" alt="abc_logo">
            <div class="order-desc">
                <template v-if="isPaid">
                    <p>您已成功支付订单，现已可登陆系统正常使用。如有其它问题，可联系您的销售经理 <span class="seller-name">{{ orderInfo.createdByName }}</span></p>
                </template>
                <template v-else>
                    <p>您好，ABC数字医疗云的销售经理 <span class="seller-name">{{ orderInfo.createdByName }}</span></p>
                    <p>向您发起了一笔待支付订单，请确认后完成支付</p>
                </template>
            </div>
        </oa-card>

        <oa-cell-group label-width="90px" class="order-info-cell">
            <oa-cell label="购买诊所">{{ orderInfo.clinicName }}</oa-cell>
            <oa-cell label="订单类型">{{ orderInfo.formatOrderType }}</oa-cell>
            <oa-cell v-if="orderInfo.formatOrderDetail" label="订单明细">{{ orderInfo.formatOrderDetail }}</oa-cell>
            <oa-cell v-if="orderInfo.formatServiceTime" label="服务时间">{{ orderInfo.formatServiceTime }}</oa-cell>
            <!--  产品冲突，暂时先隐藏订单金额 -->
            <oa-cell v-if="orderInfo.totalPrice && false" label="订单金额">
                <span>{{ formatMoney(orderInfo.totalPrice, '￥') }}</span>
            </oa-cell>
            <oa-cell v-if="orderInfo.adjustmentPrice" label="折扣优惠">
                <span>{{ formatMoney(orderInfo.adjustmentPrice, '￥') }}</span>
            </oa-cell>
            <oa-cell v-if="orderInfo.discountFee" label="用券抵扣">
                <span>{{ formatMoney(orderInfo.discountFee, '￥') }}</span>
            </oa-cell>
            <oa-cell v-if="orderInfo.deductionFee" label="抵扣金额">
                <span>{{ formatMoney(orderInfo.deductionFee, '￥') }}</span>
            </oa-cell>
            <oa-cell v-if="orderInfo.prePaidFee" label="已付金额">
                <span>{{ formatMoney(orderInfo.prePaidFee, '￥') }}</span>
            </oa-cell>
            <oa-cell label="应付金额" style="width: 100%;" class="pay-amount-cell">
                <span class="money">{{ formatMoney(orderInfo.receivableFee, '￥') }}</span>
                <img
                    v-if="isNewPurchaseOrder"
                    class="refund-goods-icon"
                    src="@/assets/qi-tian-wu-li-you-refund-goods.png"
                    alt="七天无理由退货"
                >
            </oa-cell>
            <oa-cell label="订单编号">{{ orderInfo.id }}</oa-cell>
        </oa-cell-group>
        <img v-if="statusMarkImg" class="img-status-mark" :src="statusMarkImg">
    </oa-card>
</template>

<style lang="scss">
    .order-detail-card {
        padding-bottom: 0;
        position: relative;

        .order-desc-card {
            padding: var(--oa-padding-16);
            background-color: #f8f8fa;

            img.logo {
                width: 110px;
                margin-top: 4px;
            }

            .order-desc {
                margin-top: var(--oa-padding-12);

                span.seller-name {
                    color: var(--oa-primary-color);
                    font-weight: 500;
                }

                p {
                    color: var(--oa-text-color-3);
                    line-height: 18px;
                }
            }
        }

        .order-info-cell {
            font-size: var(--oa-font-size-14);
            padding: 0 6px;
        }

        .money {
            color: var(--oa-orange);
        }

        .img-status-mark {
            position: absolute;
            right: 0;
            top: 0;
            width: 74px;
        }

        .pay-amount-cell {
            .oa-cell__value-wrapper {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .refund-goods-icon {
                    width: 100px;
                }
            }
        }
    }
</style>
