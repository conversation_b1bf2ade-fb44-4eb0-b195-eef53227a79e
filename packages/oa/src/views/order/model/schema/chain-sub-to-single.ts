import { NodeTypeFilter } from '@/vendor/x-form/types/model';

/**
 * 发放优惠券订单提交参数
 * @param formData
 */
export function createPostData(formData: any): AbcAPI.StripClinicContentRequest {
    return {
        // 门店id
        clinicId: formData.clinicId,
        // 门店名称，前端可以不传，后台会获取
        clinicName: '',
        // 推荐截图
        attachments: formData.attachments,
        // 备注
        remark: formData.remark,
    };
}

export const formSchema = {
    type: 'object',
    component: 'SubmitForm',
    labelPosition: 'top',
    actions: {
        submit: {
            type: 'submit',
            component: 'Button',
            loading: false,
            componentProps: {
                text: '创建',
                type: 'primary',
            },
        },
    },
    properties: {
        clinicId: {
            label: '选择要转单店的连锁子店',
            type: 'string',
            component: 'OrganPicker',
            componentProps: {
                nodeTypeFilter: NodeTypeFilter.CHAIN_SUB,
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },

        remark: {
            label: '转为单店的原因',
            type: 'string',
            component: 'Textarea',
            componentProps: {
                placeholder: '内部审批时可见',
                maxLength: 50,
            },
        },

        attachments: {
            label: '用户告知书截图',
            type: 'array',
            defaultValue: [],
            component: 'Images',
            componentProps: {
                placeholder: '上传截图',
                // 上传目录
                rootDir: 'oa/order',
            },
            rules: [
                { required: true, message: '必填' },
            ],
        },
    },
};
