/*
 * <AUTHOR>
 * @DateTime 2020-09-01 17:53:19
 */
// 订单范围
import { HisType } from '@/utils/clinic';

export const orderScopeOptions = [
    { text: '全部订单', value: 0 },
    { text: '我的订单', value: 1 },
];

// 订单状态
export const orderStatusOptions = [
    { text: '全部状态', value: null },
    { text: '待付款', value: 0 },
    { text: '已付款', value: 10 },
    { text: '已取消', value: 90 },
];

// 版本
export const editionAllOptions = [
    // 诊所管家
    { value: '10', label: '基础版', hisType: HisType.NORMAL },
    { value: '20', label: '专业版', hisType: HisType.NORMAL },
    { value: '30', label: '旗舰版', hisType: HisType.NORMAL },
    { value: '40', label: '大客户', hisType: HisType.NORMAL },
    // 口腔管家
    { value: '10', label: '基础版', hisType: HisType.DENTISTRY },
    { value: '20', label: '专业版', hisType: HisType.DENTISTRY },
    { value: '30', label: '旗舰版', hisType: HisType.DENTISTRY },
    { value: '40', label: '大客户', hisType: HisType.DENTISTRY },
    // 眼视光管家
    { value: '210', label: '基础版', disabled: true, hisType: HisType.OPHTHALMOLOGY },
    { value: '220', label: '标准版', hisType: HisType.OPHTHALMOLOGY },
    { value: '230', label: '专业版', hisType: HisType.OPHTHALMOLOGY },
    { value: '240', label: '旗舰版', hisType: HisType.OPHTHALMOLOGY },
    // 药店管家
    { value: '2010', label: '基础版', hisType: HisType.PHARMACY },
    { value: '2020', label: '专业版', hisType: HisType.PHARMACY },
];

export const editionLabelOptions = {
    basicVersion: {
        values: ['10', '210', '2010'],
        label: '基础版',
    },
    professionalEdition: {
        values: ['20', '230', '2020'],
        label: '专业版',
    },
    standardEdition: {
        values: ['220'],
        label: '标准版',
    },
    ultimate: {
        values: ['30', '240'],
        label: '旗舰版',
    },
    bigCustomerVersion: {
        values: ['40'],
        label: '大客户版',
    },
};
export const getEditionOptionsByHisType = (hisType: any): any[] => editionAllOptions.filter(item => item.hisType === hisType);

export const editionOptions = getEditionOptionsByHisType(HisType.NORMAL);
/**
 * @description: 获取版本选项
 * @date: 2024-03-07 15:36:57
 * @author: Horace
 * @param {any} hisType 产品类型
 * @param {boolean} isChain 是否连锁
 * @return
*/
export const getEditionOptions = (hisType: any, isChain = false) => {
    let options = getEditionOptionsByHisType(hisType);
    options = !options || options.length === 0 ? editionOptions : options;
    if (isChain && hisType !== HisType.PHARMACY) {
        return options.filter(
            // 连锁版本，排除基础版
            item => !editionLabelOptions.basicVersion.values.includes(item.value),
        ).filter(
            // 眼科连锁，排序基础版、标准版
            item => !editionLabelOptions.standardEdition.values.includes(item.value),
        );
    }
    return options;
};
// 支付方式
export const payModeOptions = [
    { value: 100, label: '线下支付' },
    { value: 1, label: '预存款支付' },
    { value: 2, label: '微信' },
    { value: 3, label: '支付宝' },
];

// 订单类型
export const orderTypeOptions = [
    { value: 1, label: '首购' },
    { value: 2, label: '续费' },
    { value: 3, label: '升级' },
];

// 经营方式
export const businessModeOptions = [
    { value: 0, label: '普通单店' },
    { value: 1, label: '连锁总店' },
    { value: 2, label: '连锁子店' },
];
