<script setup lang="ts">

import { LuckyItem, useLotteryStore } from '@/views/lottery/lottery-store';
import { computed, nextTick, onBeforeUpdate, onMounted, onUnmounted, reactive } from 'vue';

const lotteryStore = useLotteryStore();

interface ShuffleTestResult {
    name: string;
    count: number[];
}

const vmData = reactive({
    width: 400,
    height: 400,
    tags: [],
    isRunning: false,
    awardsOptions: [
        { label: '一等奖(￥5000现金)', value: 1 },
        { label: '二等奖(￥3000现金)', value: 2 },
        { label: '三等奖(￥2000京东卡)', value: 3 },
        { label: '四等奖(￥1000京东卡)', value: 4 },
        { label: '五等奖(￥500京东卡)', value: 5 },
        { label: '阳光普照(￥200京东卡)', value: 6 },
    ],
    awardCount: 5,
    awardCountOptions: [
        29,
        28,
        5,
        4,
        3,
        2,
        1,
    ],
    currentAward: 1,
    currentLuckyPeople: <LuckyItem[]>[],
    visibleResult: false,
    bgmList: [
        { url: 'https://abc-fed.oss-cn-chengdu.aliyuncs.com/lottery/lottery-bgm.ogg' },
        { url: 'https://abc-fed.oss-cn-chengdu.aliyuncs.com/lottery/lottery-hyl.ogg' },
    ],
    bgmPlayers: <HTMLAudioElement[]>[],
    visibleShuffleTest: false,
    shuffleTestResult: <ShuffleTestResult[]><unknown>[],
});

const canvasId = 'tag-canvas';

const tags = computed(() => {
    const tags: any[] = [];
    for (let i = 0; i < lotteryStore.peopleList.length; i++) {
        let tag:any = {};
        tag.text = lotteryStore.peopleList[i].name;
        tag.type = lotteryStore.peopleList[i].type;
        tag.awarded = lotteryStore.awardedPeopleList.has(lotteryStore.peopleList[i].name);
        tags.push(tag);
    }
    return tags;
});

const currentAwardLabel = computed(() => vmData.awardsOptions.find(item => item.value === vmData.currentAward)!.label);

function getResultByAward(award: any) {
    return lotteryStore.result[<keyof typeof lotteryStore.result>award];
}

function toggle() {
    if (vmData.isRunning) {
        // eslint-disable-next-line no-undef
        TagCanvas.SetSpeed(canvasId, speed());
        vmData.currentLuckyPeople = lotteryStore.lottery(vmData.currentAward, vmData.awardCount);
        vmData.bgmPlayers.forEach(player => player.pause());
    } else {
        // eslint-disable-next-line no-undef
        TagCanvas.SetSpeed(canvasId, [10, -6]);
        // 随机选一个 bgm 播放
        const currentPlayer = vmData.bgmPlayers[Math.floor(Math.random() * 10) % 2];
        currentPlayer.play();
    }
    vmData.isRunning = !vmData.isRunning;
    nextTick(() => {
        // eslint-disable-next-line no-undef
        TagCanvas.Update(canvasId);
    });
}

function speed() {
    return [0.1 * Math.random() + 0.01, -(0.1 * Math.random() + 0.01)];
}

function onLayoutResize() {
    const totalHeight = document.body.clientHeight;
    const size = totalHeight - 69 - 30 - 32 - 46 - 34 - 30;
    vmData.height = size;
    vmData.width = size;
    nextTick(() => {
        // eslint-disable-next-line no-undef
        TagCanvas.Update(canvasId);
    });
}

function bindBgmRef(el: any) {
    if (el) {
        // @ts-ignore
        vmData.bgmPlayers.push(<HTMLAudioElement>el);
    }
}

function handleTestShuffle() {
    const countObj = lotteryStore.testShuffle();
    // @ts-ignore
    vmData.shuffleTestResult = <{name: string, count: number[]}>Object.keys(countObj)
                    .map((name) => ({ name, count: countObj[name] }));
}

function handleTestShuffleShortcut(e: KeyboardEvent) {
    if (e.ctrlKey && e.key === 'F2') {
        vmData.visibleShuffleTest = true;
    }
}

onBeforeUpdate(() => { vmData.bgmPlayers = []; });

onMounted(() => {
    lotteryStore.initPeopleList();
    onLayoutResize();
    window.addEventListener('resize', onLayoutResize);
    document.addEventListener('keydown', handleTestShuffleShortcut);

    nextTick(() => {
        // eslint-disable-next-line no-undef
        // TagCanvas.textColour = '#ffffff';
        // eslint-disable-next-line no-undef
        TagCanvas.Start(canvasId, 'tags', {
            initial: speed(),
            dragControl: 1,
            textHeight: 22,
            zoom: 1.04,
            zoomMax: 1.04,
            zoomMin: 1.04,
            outlineColour: 'transparent',
            textColour: null,
        });
    });
});

onUnmounted(() => {
    window.removeEventListener('resize', onLayoutResize);
    document.removeEventListener('keydown', handleTestShuffleShortcut);
});

</script>
<template>
    <div class="lottery-wrapper">
        <div v-if="vmData.currentLuckyPeople.length" class="cover"></div>
        <img class="title" src="@/assets/lottery/lottery-title.png">

        <el-select
            v-model="vmData.currentAward"
            class="award-select"
            popper-class="award-select-popper"
            fit-input-width
            :popper-append-to-body="false"
        >
            <el-option
                v-for="item in vmData.awardsOptions"
                :label="item.label"
                :value="item.value"
            ></el-option>
        </el-select>

        <div class="tag-canvas-wrapper">
            <canvas id="tag-canvas" :width="vmData.width" :height="vmData.height"></canvas>
            <div id="tags">
                <ul>
                    <li
                        v-for="tag in tags"
                        :key="tag.text"
                        :class="{'is-awarded': tag.awarded}"
                    >
                        <a @click.prevent>{{ tag.text }}</a>
                    </li>
                </ul>
            </div>
            <img class="bg-circle" src="@/assets/lottery/<EMAIL>">
        </div>
        <div class="control-wrapper">
            <ul class="count-wrapper">
                <li
                    v-for="c in vmData.awardCountOptions"
                    :key="c"
                    :class="['count-item', c === vmData.awardCount ? 'is-selected' : '']"
                    @click="vmData.awardCount = c"
                >
                    {{ c }}
                </li>
                <li class="toggle-btn" @click="toggle">{{ vmData.isRunning ? '停止' : '开始' }}</li>
            </ul>
        </div>

        <div class="lottery-result-btn" @click="vmData.visibleResult = true">获奖名单</div>

        <img class="bg-left-top" src="@/assets/lottery/bg-left-top.png">
        <img class="bg-right-bottom" src="@/assets/lottery/bg-right-bottom.png">
        <audio
            v-for="bgm in vmData.bgmList"
            :key="bgm.url"
            :ref="bindBgmRef"
            :src="bgm.url"
        ></audio>
    </div>

    <div
        v-if="vmData.currentLuckyPeople.length"
        class="current-lucky-wrapper"
    >
        <h2 class="award-label">{{ currentAwardLabel }}</h2>
        <div class="current-lucky-item-wrapper">
            <div v-for="item in vmData.currentLuckyPeople" :key="item.name" class="current-lucky-item">
                {{ item.name }}
            </div>
        </div>
        <div
            class="close-btn"
            src="@/assets/lottery/close.png"
            @click="vmData.currentLuckyPeople.length = 0"
        >
            关闭
        </div>
    </div>

    <div
        v-if="vmData.visibleResult"
        class="lottery-result-wrapper"
        @click="vmData.visibleResult = false"
    >
        <div class="title">
            <img class="title" src="@/assets/lottery/<EMAIL>">
        </div>
        <div class="award-wrapper">
            <section v-for="(award, index) in vmData.awardsOptions" :key="index">
                <h2>{{ award.label }}</h2>
                <div class="lucky-item-wrapper">
                    <span
                        v-for="lucky in getResultByAward(index + 1)"
                        :key="lucky.name"
                        class="lucky-item"
                    >{{ lucky.name }}</span>
                </div>
            </section>
        </div>
    </div>

    <div
        v-if="vmData.visibleShuffleTest"
        class="lottery-test-wrapper"
    >
        <div class="code-wrapper">
            <pre>
                function shuffle(arr) {
                    let m = arr.length;
                    while (m > 1) {
                        let index = Math.floor(Math.random() * m--);
                        [arr[m], arr[index]] = [arr[index], arr[m]];
                    }
                    return arr;
                }
                <a href="https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle" target="_blank">
                Fisher–Yates shuffle
                </a>
            </pre>
            <div class="test-btn" @click.stop.prevent="handleTestShuffle">开始测试</div>
        </div>
        <div class="result-wrapper">
            <div class="row">
                <div class="col">姓名</div>
                <div
                    v-for="(p, index) in vmData.shuffleTestResult"
                    :key="p.name"
                    class="col"
                >
                    {{ index }}
                </div>
            </div>
            <div
                v-for="(p, index) in vmData.shuffleTestResult"
                :key="index"
                class="row"
            >
                <div class="col">{{ p.name }}</div>
                <div
                    v-for="(c, index) in p.count"
                    :key="index"
                    class="col"
                >
                    {{ c }}
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss">
.lottery-wrapper {
    width: 100vw;
    height: 100vh;
    background-color: #000134;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding: 0;
    margin: 0;

    .cover {
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        background: rgba(0, 1, 52, .29);
        backdrop-filter: blur(5px);
        z-index: 998;
    }

    img.title {
        width: 280px;
        margin-top: 30px;
    }

    .award-select {
        z-index: 2;

        .el-input__wrapper {
            width: 265px;
            height: 40px;
            background: #050f58;
            box-shadow: inset 0 1px 10px 0 rgba(70, 226, 255, .29);
            border-radius: 3px;
            font-size: 19px;
            color: #fff;
            line-height: 27px;
            letter-spacing: 2px;
            border: none !important;
        }
    }

    .tag-canvas-wrapper {
        width: 100%;
        z-index: 1;
        display: flex;
        justify-content: center;
        position: relative;
        flex: 1;

        img.bg-circle {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            height: 125%;
            z-index: -1;
            user-select: none;
            pointer-events: none;
        }

        li {
            color: #fff;

            &.is-awarded {
                color: #42b9e3;
            }
        }
    }

    .control-wrapper {
        display: flex;
        height: 46px;
        margin-bottom: 34px;

        .count-wrapper {
            display: flex;

            > li {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 45px;
                height: 45px;
                background: #111f69;
                border-radius: 3px;
                font-size: 23px;
                color: #fff;

                & + li {
                    margin-left: 7px;
                }

                &.count-item {
                    cursor: pointer;

                    &.is-selected {
                        color: #b728b4;
                    }
                }

                &.toggle-btn {
                    font-size: 17px;
                    background: linear-gradient(90deg, #9b1ba1 0%, #4427c7 100%);
                    border-radius: 3px;
                }
            }
        }
    }

    .lottery-result-btn {
        position: absolute;
        right: 0;
        top: 44px;
        width: 136px;
        height: 47px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #0d1a66;
        box-shadow: inset 0 1px 10px 0 rgba(57, 179, 255, .27);
        border-radius: 56px 0 0 56px;
        font-size: 23px;
        font-weight: 400;
        color: #42b9e3;
        line-height: 32px;
        letter-spacing: 2px;
        user-select: none;
        cursor: pointer;
    }

    .bg-left-top {
        position: absolute;
        top: 0;
        left: 0;
        width: 26%;
    }

    .bg-right-bottom {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 26%;
    }
}

.current-lucky-wrapper {
    position: fixed;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 999;
    top: 0;
    left: 0;

    .award-label {
        height: 36px;
        font-size: 24px;
        font-weight: 500;
        color: #fff;
        line-height: 48px;
        letter-spacing: 3px;
        text-align: center;
        margin-bottom: 34px;
        margin-top: -200px;
    }

    .current-lucky-item-wrapper {
        display: flex;
        flex-wrap: wrap;
        padding: 0 20px;

        .current-lucky-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 169px;
            height: 91px;
            background: #050f58;
            box-shadow: inset 0 1px 21px 0 rgba(70, 226, 255, .29);
            border-radius: 5px;
            margin: 14px;
            color: #fff;
            font-weight: 500;
            line-height: 55px;
            font-size: 39px;
            letter-spacing: 5px;
        }
    }

    .close-btn {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 60px;
        background: #050f58;
        box-shadow: inset 0 1px 21px 0 rgba(70, 226, 255, .29);
        border-radius: 5px;
        margin: 14px;
        color: #fff;
        font-weight: 500;
        line-height: 55px;
        font-size: 20px;
        letter-spacing: 5px;
    }
}

.lottery-result-wrapper {
    position: fixed;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 999;
    top: 0;
    left: 0;
    background: #000134;
    overflow: auto;
    padding: 30px 60px 0 60px;

    .title {
        height: 68px;
        font-size: 68px;
        color: #fff;
        line-height: 68px;
        letter-spacing: 8px;
        text-shadow: 3px 3px 11px #b728b4;
        user-select: none;
    }

    .award-wrapper {
        height: 100%;
        overflow: auto;
    }

    section + section {
        margin-top: 40px;
    }

    h2 {
        height: 27px;
        font-size: 19px;
        font-weight: 400;
        color: #fff;
        line-height: 48px;
        letter-spacing: 3px;
        text-align: center;
        margin-bottom: 34px;
    }

    .lucky-item-wrapper {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;

        .lucky-item {
            margin-right: 30px;
            margin-left: 30px;
            width: 114px;
            height: 47px;
            font-size: 34px;
            font-weight: 400;
            color: #42b9e3;
            line-height: 47px;
            letter-spacing: 4px;
            margin-top: 6px;
        }
    }
}

.lottery-test-wrapper {
    position: fixed;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 999;
    top: 0;
    left: 0;
    background: #000134;
    overflow-x: auto;
    padding: 30px 60px 0 60px;

    .code-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 180px;
        position: relative;
        flex-shrink: 0;

        pre {
            color: white;
            position: absolute;
            left: 0;
            top: 0;

            a {
                color: gray;
            }
        }
    }

    .test-btn {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 60px;
        background: #050f58;
        box-shadow: inset 0 1px 21px 0 rgba(70, 226, 255, .29);
        border-radius: 5px;
        margin: 14px;
        color: #fff;
        font-weight: 500;
        line-height: 55px;
        font-size: 20px;
    }

    .result-wrapper {
        width: 100%;
        overflow: auto;

        .row {
            display: flex;
        }

        .col {
            flex-shrink: 0;
            width: 60px;
            color: #fff;
        }
    }
}

.award-select-popper {
    background: #050f58;
    box-shadow: inset 0 1px 10px 0 rgba(70, 226, 255, .29);
    border-radius: 3px;
    border: none !important;
    margin-top: -6px;

    .el-popper__arrow {
        display: none;
    }

    .el-select-dropdown__item {
        display: flex;
        align-items: center;
        height: 41px;
        font-size: 19px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #fff;
        line-height: 27px;
        letter-spacing: 1px;

        &.selected {
            background: rgba(66, 185, 227, .21);
        }

        &.hover {
            background: rgba(66, 185, 227, .21) !important;
        }

        &:hover {
            background: rgba(66, 185, 227, .21) !important;
        }
    }
}
</style>
