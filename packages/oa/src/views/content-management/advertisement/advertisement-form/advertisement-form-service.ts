import SaveOrUpdateCmsPushReq = AbcAPI.SaveOrUpdateCmsPushReq;
import CmsPushResourceDto = AbcAPI.CmsPushResourceDto;
import CmsPushRegionDto = AbcAPI.CmsPushRegionDto;
import { CmsAPI } from '@/api/cms-api';

export default class AdvertisementFormService {
    private postData: SaveOrUpdateCmsPushReq;

    constructor() {
        this.postData = {
            adType: undefined, // 广告类型
            condition: {
                prodTypeList: [],
                organTypeList: [],
                prodVersionList: [],
                functionIncludeList: [],
                functionExcludeList: [],
                clinicRoleList: [],
                newOrOldCustomer: 1,
            },
            releaseVersion: '', // 发布版本 adType 为 2:迭代推送 时有效
            pushResList: <CmsPushResourceDto[]>[], // 推送资源列表
            includeClinicIds: [], // 附加的推送门店ID列表
            excludeClinicIds: [], // 排除的门店ID列表
            regions: <CmsPushRegionDto[]>[], // 展示地区
        };
    }

    async create(data: SaveOrUpdateCmsPushReq) {
        await CmsAPI.saveCmsPushUsingPOST(data);
    }

    async update(id: string, data: SaveOrUpdateCmsPushReq) {
        await CmsAPI.updateCmsPushUsingPUT(data, id);
    }

    getPostData() {
        return this.postData;
    }

    async fetchData(id: string) {
        this.postData = await CmsAPI.getOneCmsPushUsingGET(id);
    }
}