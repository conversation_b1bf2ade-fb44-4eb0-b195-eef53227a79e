server {
    listen 9090;
    root /workspace/www;

    access_log /workspace/log/access.log;
    error_log   /workspace/log/error.log;

    location ~ \.(jpg|png|jpeg|gif)$ {
        expires 30d;
    }

    location ~ \.(js|css)$ {
        expires 1d;
    }

    location /WW_verify_z7xa0W9pu3XObSb8.txt {
        alias /workspace/www/WW_verify_z7xa0W9pu3XObSb8.txt;
    }

    location /MP_verify_KApWWgALwoItGmHz.txt {
        alias /workspace/www/MP_verify_KApWWgALwoItGmHz.txt;
    }

    location /MP_verify_xa8DbbEzR32OamJE.txt {
        alias /workspace/www/MP_verify_xa8DbbEzR32OamJE.txt;
    }

    location /MP_verify_59tlT96o5Pw33xbY.txt {
        alias /workspace/www/MP_verify_59tlT96o5Pw33xbY.txt;
    }

    location / {
        if (-f $request_filename){
          break;
        }

        add_header Cache-Control no-store;

        rewrite /share/* /share-page.html break;
        rewrite /help-center-customer/* /help-center.html break;
        rewrite /* /index.html break;
    }
}
